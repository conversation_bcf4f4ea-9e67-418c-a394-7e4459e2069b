---
type: 'always_apply'
---

1. **Auto Cleanup on Delete**: This rule automatically removes instances and
   references of deleted files from other parts of the codebase, but only after
   ensuring necessary logic is migrated when deemed appropriate. It is triggered
   when a file is deleted and involves asking an agent to analyze the deleted
   file and the remaining codebase to identify references, determine if critical
   logic needs migration, suggest migration locations, remove references after
   confirming migration, and update related configuration files and
   documentation.

2. **ChromaDB Vector Guard**: This rule monitors code changes to ensure vector
   embeddings are always accomplished using ChromaDB, preventing pollution of
   the vector database and ensuring complete coverage of embeddings and vector
   storage in the knowledge pipeline. It is triggered when specific file types
   are edited and involves asking an agent to review the code changes to ensure
   proper ChromaDB usage and flag any violations.

3. **Code Quality Analyzer**: This rule monitors source code files for changes
   and analyzes modified code for potential improvements, including code smells,
   design patterns, and best practices. It is triggered when specific file types
   are edited and involves asking an agent to analyze the code and provide
   suggestions for improvements while maintaining existing functionality.

4. **CS Principles Enforcer**: This rule automatically reviews code changes to
   enforce core computer science principles such as DRY, KISS, SRP, Separation
   of Concerns, Fail Fast, and proper modularity. It is triggered when specific
   file types are edited and involves asking an agent to review the changed
   files for violations of these principles and suggest specific refactoring
   improvements.

5. **DaisyUI Component Enforcer**: This rule monitors UI component files and
   ensures the daisyUI library with tailwindcss is used for component creation
   with current documentation. It is triggered when specific file types are
   edited and involves asking an agent to review the changed files to ensure
   they follow daisyUI patterns and conventions.

6. **Documentation Sync**: This rule listens to JavaScript source files and
   configuration changes to automatically update project documentation. It is
   triggered when specific file types are edited and involves asking an agent to
   review the changes and update the project documentation accordingly.

7. **CDN Usage Prevention**: This rule monitors HTML and JavaScript files for
   CDN links and suggests npm package alternatives to ensure the application
   works offline. It is triggered when specific file types are created and
   involves asking an agent to review the modified files for CDN links and
   suggest equivalent npm packages.

8. **Structure Enforcement**: This rule monitors code changes to enforce proper
   project structure, file organization, naming conventions, and architectural
   patterns as defined in the steering docs. It is triggered when specific file
   types are edited and involves asking an agent to review the changed files to
   ensure they follow the proper structure and provide specific feedback on any
   violations.

9. **Auto Test Coverage**: This rule monitors source file changes to identify
   new/modified functions, check test coverage, generate missing tests, run
   tests, and update coverage reports. It is triggered when specific file types
   are edited and involves asking an agent to analyze the changed file, identify
   new or modified functions, check test coverage, generate missing tests, run
   the test suite, and update coverage reports.

10. **Unused Variables Implementation Guard**: This rule monitors file changes
    to detect unused variables and ensures they are properly implemented in the
    intended feature rather than being removed as shortcuts to production
    readiness. It is triggered when specific file types are created and involves
    asking an agent to analyze the code for unused variables, determine their
    intended purpose, provide implementation guidance, and ensure the feature is
    fully implemented before considering the code production-ready.

11. The complete Project management knowledgte base is available at
    @Project-management directory. It is triggered everytime after a new chat is
    initiated with the agent, Project-management ├── design.md ├── product.md
    ├── requirements.md ├── structure.md ├── tasks.md └── tech.md
12. The package,json should be read always before the chat interaction begins.
