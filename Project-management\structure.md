---
type: 'agent_requested'
description: 'Example description'
---

# Project Structure and Organization

## Enterprise-Grade Directory Architecture

```
ai-document-processor/
├── .git/                           # Git version control with LFS for large files
├── .github/                        # GitHub workflows and templates
│   ├── workflows/                  # CI/CD pipelines and automation
│   └── ISSUE_TEMPLATE/            # Issue and PR templates
├── .kiro/                         # Kiro IDE configuration and specifications
│   ├── steering/                  # AI assistant guidance documents
│   ├── specs/                     # Feature specifications and design docs
│   └── settings/                  # IDE-specific settings and configurations
├── .vscode/                       # VS Code workspace configuration
│   ├── settings.json              # Editor settings and extensions
│   └── launch.json                # Debug configurations
├── docs/                          # Comprehensive project documentation
│   ├── api/                       # API documentation and schemas
│   ├── architecture/              # System architecture diagrams
│   ├── user-guide/               # End-user documentation
│   └── development/              # Developer setup and guidelines
├── tests/                         # Comprehensive testing infrastructure
│   ├── unit/                     # Unit tests with mocks and fixtures
│   ├── integration/              # Integration tests with real services
│   ├── e2e/                      # End-to-end tests with Playwright
│   ├── performance/              # Performance benchmarking tests
│   ├── security/                 # Security and vulnerability tests
│   └── fixtures/                 # Test data and mock files
├── scripts/                       # Build, deployment, and utility scripts
│   ├── build/                    # Build automation scripts
│   ├── deploy/                   # Deployment and packaging scripts
│   ├── db/                       # Database management scripts
│   └── dev/                      # Development utility scripts
├── assets/                        # Static assets and resources
│   ├── icons/                    # Application icons (multiple sizes)
│   ├── images/                   # UI images and graphics
│   ├── fonts/                    # Custom fonts and typography
│   └── templates/                # Document templates and samples
├── public/                        # Build output and static files
│   ├── output.css                # Compiled TailwindCSS
│   ├── assets/                   # Processed static assets
│   └── locales/                  # Internationalization files
├── src/                          # Source code (multi-process architecture)
│   ├── main/                     # Electron main process (Node.js/TypeScript)
│   │   ├── index.ts              # Main process entry point
│   │   ├── services/             # Core business logic services
│   │   │   ├── DocumentProcessor.ts      # Document processing orchestration
│   │   │   ├── PDFProcessor.ts           # PDF-specific processing
│   │   │   ├── ExcelProcessor.ts         # Excel/spreadsheet processing
│   │   │   ├── WordProcessor.ts          # Word document processing
│   │   │   ├── CSVProcessor.ts           # CSV data processing
│   │   │   ├── OCREngine.ts              # OCR and image processing
│   │   │   ├── ImageProcessor.ts         # Image enhancement and analysis
│   │   │   ├── AIModelClient.ts          # AI service integration
│   │   │   ├── AzureAIClient.ts          # Azure AI specific client
│   │   │   ├── OpenAIClient.ts           # OpenAI specific client
│   │   │   ├── LangChainAgent.ts         # LangChain agent orchestration
│   │   │   ├── KnowledgeBaseService.ts   # ChromaDB knowledge management
│   │   │   ├── NLPProcessor.ts           # Natural language processing
│   │   │   ├── FormFillerService.ts      # Intelligent form filling
│   │   │   ├── TemplateManager.ts        # Template creation and management
│   │   │   ├── TimelineManager.ts        # Version control and history
│   │   │   ├── DiffEngine.ts             # Document comparison engine
│   │   │   ├── CalculationEngine.ts      # Mathematical computations
│   │   │   ├── EncryptionService.ts      # Security and encryption
│   │   │   ├── CacheManager.ts           # Multi-level caching
│   │   │   └── FileManager.ts            # File system operations
│   │   ├── workers/              # Worker processes for CPU-intensive tasks
│   │   │   ├── WorkerManager.ts          # Worker thread coordination
│   │   │   ├── AIWorker.ts               # AI processing worker
│   │   │   ├── OCRWorker.ts              # OCR processing worker
│   │   │   ├── DocumentWorker.ts         # Document processing worker
│   │   │   └── CalculationWorker.ts      # Mathematical computation worker
│   │   ├── database/             # Database management and migrations
│   │   │   ├── connection.ts             # Database connection setup
│   │   │   ├── migrations/               # Knex.js database migrations
│   │   │   │   ├── 001_create_documents_table.ts
│   │   │   │   ├── 002_create_extracted_data_table.ts
│   │   │   │   ├── 003_create_knowledge_base_table.ts
│   │   │   │   ├── 004_create_timeline_table.ts
│   │   │   │   ├── 005_create_templates_table.ts
│   │   │   │   └── 006_create_user_sessions_table.ts
│   │   │   └── seeds/                    # Database seed data
│   │   ├── ipc/                  # Inter-process communication handlers
│   │   │   ├── documentHandlers.ts       # Document processing IPC
│   │   │   ├── aiHandlers.ts             # AI service IPC
│   │   │   ├── knowledgeHandlers.ts      # Knowledge base IPC
│   │   │   ├── timelineHandlers.ts       # Timeline/version control IPC
│   │   │   └── fileHandlers.ts           # File system IPC
│   │   └── utils/                # Main process utilities
│   │       ├── logger.ts                 # Structured logging
│   │       ├── config.ts                 # Configuration management
│   │       ├── security.ts               # Security utilities
│   │       └── performance.ts            # Performance monitoring
│   ├── renderer/                 # React frontend (TypeScript/JSX)
│   │   ├── index.tsx             # React application entry point
│   │   ├── App.tsx               # Main application component
│   │   ├── components/           # React UI components
│   │   │   ├── layout/           # Layout and structural components
│   │   │   │   ├── TitleBar.tsx          # Custom window title bar
│   │   │   │   ├── Sidebar.tsx           # Resizable sidebar layout
│   │   │   │   ├── StatusBar.tsx         # Status and progress bar
│   │   │   │   ├── MainContent.tsx       # Main content area
│   │   │   │   └── TabContainer.tsx      # Tab management system
│   │   │   ├── explorer/         # File explorer components
│   │   │   │   ├── FileTree.tsx          # VSCode-style file tree
│   │   │   │   ├── FileItem.tsx          # Individual file items
│   │   │   │   ├── FileSearch.tsx        # File search and filtering
│   │   │   │   └── ProjectView.tsx       # Project management view
│   │   │   ├── editors/          # Document editor components
│   │   │   │   ├── DocumentEditor.tsx    # Monaco-based document editor
│   │   │   │   ├── PDFViewer.tsx         # PDF viewing and annotation
│   │   │   │   ├── FormEditor.tsx        # Form field editing
│   │   │   │   ├── TextEditor.tsx        # Text document editing
│   │   │   │   └── DiffViewer.tsx        # Side-by-side diff viewer
│   │   │   ├── tabs/             # Tab management components
│   │   │   │   ├── TabBar.tsx            # Tab bar with drag/drop
│   │   │   │   ├── Tab.tsx               # Individual tab component
│   │   │   │   └── TabGroup.tsx          # Tab grouping functionality
│   │   │   ├── timeline/         # Version control components
│   │   │   │   ├── TimelineViewer.tsx    # Timeline visualization
│   │   │   │   ├── CheckpointList.tsx    # Checkpoint management
│   │   │   │   ├── BranchViewer.tsx      # Branch visualization
│   │   │   │   └── MergeDialog.tsx       # Merge conflict resolution
│   │   │   ├── ai/               # AI interaction components
│   │   │   │   ├── ChatInterface.tsx     # AI chat interface
│   │   │   │   ├── AIAssistant.tsx       # AI assistant panel
│   │   │   │   ├── ProcessingStatus.tsx  # AI processing indicators
│   │   │   │   └── ModelSelector.tsx     # AI model selection
│   │   │   ├── knowledge/        # Knowledge base components
│   │   │   │   ├── KnowledgeBase.tsx     # Knowledge management UI
│   │   │   │   ├── SearchInterface.tsx   # Semantic search interface
│   │   │   │   ├── GraphViewer.tsx       # Knowledge graph visualization
│   │   │   │   └── EntityEditor.tsx      # Entity editing interface
│   │   │   ├── forms/            # Form processing components
│   │   │   │   ├── FormViewer.tsx        # Form display and editing
│   │   │   │   ├── FieldMapper.tsx       # Field mapping interface
│   │   │   │   ├── TemplateEditor.tsx    # Template creation/editing
│   │   │   │   └── ValidationPanel.tsx   # Form validation display
│   │   │   ├── annotations/      # Annotation and markup components
│   │   │   │   ├── AnnotationTool.tsx    # Annotation creation tools
│   │   │   │   ├── SignaturePad.tsx      # Digital signature interface
│   │   │   │   ├── CommentThread.tsx     # Comment threading system
│   │   │   │   └── MarkupOverlay.tsx     # Document markup overlay
│   │   │   └── common/           # Shared UI components
│   │   │       ├── Button.tsx            # Customized button component
│   │   │       ├── Modal.tsx             # Modal dialog system
│   │   │       ├── Tooltip.tsx           # Interactive tooltips
│   │   │       ├── LoadingSpinner.tsx    # Loading indicators
│   │   │       └── ErrorBoundary.tsx     # Error handling boundary
│   │   ├── hooks/                # Custom React hooks
│   │   │   ├── useDocuments.ts           # Document management hook
│   │   │   ├── useAI.ts                  # AI service integration hook
│   │   │   ├── useTimeline.ts            # Timeline/version control hook
│   │   │   ├── useKnowledge.ts           # Knowledge base hook
│   │   │   ├── useTabs.ts                # Tab management hook
│   │   │   ├── useKeyboardShortcuts.ts   # Keyboard shortcut hook
│   │   │   └── usePerformance.ts         # Performance monitoring hook
│   │   ├── stores/               # Zustand state management
│   │   │   ├── documentStore.ts          # Document state management
│   │   │   ├── tabStore.ts               # Tab state management
│   │   │   ├── aiStore.ts                # AI interaction state
│   │   │   ├── timelineStore.ts          # Timeline/version state
│   │   │   ├── knowledgeStore.ts         # Knowledge base state
│   │   │   ├── uiStore.ts                # UI state management
│   │   │   └── settingsStore.ts          # User preferences state
│   │   ├── services/             # Frontend service layer
│   │   │   ├── ipcService.ts             # IPC communication wrapper
│   │   │   ├── apiService.ts             # API communication service
│   │   │   ├── cacheService.ts           # Client-side caching
│   │   │   └── analyticsService.ts       # User analytics service
│   │   ├── styles/               # Styling and themes
│   │   │   ├── globals.css               # Global CSS styles
│   │   │   ├── components.css            # Component-specific styles
│   │   │   ├── themes.css                # Theme definitions
│   │   │   └── animations.css            # Animation definitions
│   │   └── utils/                # Frontend utilities
│   │       ├── formatting.ts             # Data formatting utilities
│   │       ├── validation.ts             # Client-side validation
│   │       ├── constants.ts              # Application constants
│   │       └── helpers.ts                # General helper functions
│   ├── shared/                   # Shared code between processes
│   │   ├── types/                # TypeScript type definitions
│   │   │   ├── Document.ts               # Document-related types
│   │   │   ├── AI.ts                     # AI service types
│   │   │   ├── Timeline.ts               # Version control types
│   │   │   ├── Knowledge.ts              # Knowledge base types
│   │   │   ├── UI.ts                     # UI component types
│   │   │   ├── IPC.ts                    # IPC message types
│   │   │   └── Database.ts               # Database schema types
│   │   ├── utils/                # Shared utility functions
│   │   │   ├── validation.ts             # Shared validation logic
│   │   │   ├── encryption.ts             # Encryption utilities
│   │   │   ├── compression.ts            # Data compression utilities
│   │   │   └── serialization.ts          # Data serialization utilities
│   │   ├── constants/            # Shared constants and enums
│   │   │   ├── errors.ts                 # Error codes and messages
│   │   │   ├── events.ts                 # Event type definitions
│   │   │   └── config.ts                 # Configuration constants
│   │   └── protocols/            # IPC protocol definitions
│   │       ├── document.ts               # Document processing protocols
│   │       ├── ai.ts                     # AI service protocols
│   │       └── timeline.ts               # Timeline management protocols
│   └── preload/                  # Preload scripts for security
│       ├── index.ts              # Main preload script
│       ├── api.ts                # Exposed API definitions
│       └── security.ts           # Security context setup
├── config/                        # Configuration files
│   ├── webpack.main.config.js    # Webpack config for main process
│   ├── webpack.renderer.config.js # Webpack config for renderer
│   ├── tsconfig.json             # TypeScript configuration
│   ├── tsconfig.main.json        # Main process TypeScript config
│   ├── tsconfig.renderer.json    # Renderer process TypeScript config
│   ├── jest.config.js            # Jest testing configuration
│   ├── playwright.config.ts      # Playwright E2E test config
│   ├── tailwind.config.js        # TailwindCSS configuration
│   ├── knexfile.js               # Database migration configuration
│   └── eslint.config.js          # ESLint configuration
├── .env.example                   # Environment variables template
├── .gitignore                     # Git ignore patterns
├── .gitattributes                 # Git LFS and line ending config
├── .eslintrc.js                   # ESLint configuration
├── .prettierrc                    # Prettier formatting config
├── .husky/                        # Git hooks configuration
├── package.json                   # Project metadata and dependencies
├── package-lock.json              # Dependency lock file
├── forge.config.js                # Electron Forge configuration
├── electron-builder.yml           # Electron Builder configuration
└── README.md                      # Project documentation
```

## Core Architecture Principles

### Multi-Process Design Pattern

- **Main Process (`src/main/`)**: System integration, file operations, database
  management, AI coordination
- **Renderer Process (`src/renderer/`)**: React UI, user interactions, state
  management, visual components
- **Worker Processes (`src/main/workers/`)**: CPU-intensive tasks, document
  processing, AI inference
- **Preload Scripts (`src/preload/`)**: Secure API bridge with context isolation
  and privilege separation

### Service-Oriented Architecture

- **Document Processing Services**: Modular processors for different file types
  with common interfaces
- **AI Integration Layer**: Unified AI client with provider abstraction and
  failover capabilities
- **Knowledge Management**: Semantic search, vector storage, and relationship
  mapping
- **Timeline System**: Git-like version control with branching, merging, and
  visual diff capabilities

### Database and Storage Strategy

- **SQLite with Migrations**: Structured data with schema versioning and
  rollback capabilities
- **ChromaDB Vector Storage**: Semantic search and embedding storage for AI
  operations
- **Multi-Level Caching**: Memory, database, and file system caching with
  intelligent invalidation
- **Encrypted Storage**: AES-256 encryption for sensitive data with secure key
  management

## Development Workflow Patterns

### Feature Development Lifecycle

1. **Specification**: Create detailed spec in `.kiro/specs/` with requirements
   and design
2. **Database Schema**: Create migration in `src/main/database/migrations/`
3. **Type Definitions**: Define TypeScript interfaces in `src/shared/types/`
4. **Service Implementation**: Implement business logic in `src/main/services/`
5. **IPC Integration**: Create IPC handlers in `src/main/ipc/`
6. **UI Components**: Build React components in `src/renderer/components/`
7. **State Management**: Implement Zustand stores in `src/renderer/stores/`
8. **Testing**: Create comprehensive tests in `tests/` directory
9. **Documentation**: Update docs and steering rules

### Code Organization Standards

#### File Naming Conventions

- **TypeScript Files**: PascalCase for classes and components
  (`DocumentProcessor.ts`, `FileTree.tsx`)
- **Utility Files**: camelCase for utilities and hooks (`useDocuments.ts`,
  `validation.ts`)
- **Configuration Files**: kebab-case for config files
  (`webpack.main.config.js`)
- **Test Files**: Match source file with `.test.ts` or `.spec.ts` suffix
- **Migration Files**: Numbered prefix with descriptive name
  (`001_create_documents_table.ts`)

#### Directory Structure Rules

- **Feature Grouping**: Group related functionality in dedicated directories
- **Layer Separation**: Clear separation between main, renderer, and shared code
- **Component Hierarchy**: Nested component directories reflecting UI structure
- **Service Organization**: Business logic services grouped by domain
- **Test Mirroring**: Test directory structure mirrors source code structure

### Import and Dependency Management

#### Import Path Conventions

```typescript
// Absolute imports from src root
import { DocumentProcessor } from '@/main/services/DocumentProcessor';
import { useDocuments } from '@/renderer/hooks/useDocuments';
import { DocumentType } from '@/shared/types/Document';

// Relative imports for same directory
import { validateDocument } from './validation';
import { ProcessingOptions } from './types';
```

#### Dependency Injection Pattern

- Services use constructor injection for dependencies
- Interface-based abstractions for testability
- Factory pattern for service instantiation
- Singleton pattern for shared resources

### Testing Strategy and Organization

#### Test Categories and Structure

- **Unit Tests (`tests/unit/`)**: Individual function and class testing with
  mocks
- **Integration Tests (`tests/integration/`)**: Service integration with real
  dependencies
- **E2E Tests (`tests/e2e/`)**: Full user workflow testing with Playwright
- **Performance Tests (`tests/performance/`)**: Load testing and benchmarking
- **Security Tests (`tests/security/`)**: Vulnerability and penetration testing

#### Test Data Management

- **Fixtures (`tests/fixtures/`)**: Sample documents and test data
- **Factories**: Programmatic test data generation
- **Cleanup**: Automatic test environment cleanup
- **Isolation**: Each test runs in isolated environment

### Build and Deployment Pipeline

#### Build Process Stages

1. **Type Checking**: TypeScript compilation and type validation
2. **Linting**: ESLint code quality and style checking
3. **Testing**: Comprehensive test suite execution
4. **Bundling**: Webpack compilation and optimization
5. **Packaging**: Electron Forge application packaging
6. **Distribution**: Multi-platform installer creation

#### Environment Configuration

- **Development**: Hot reload, DevTools, debug logging
- **Testing**: Test database, mock services, coverage reporting
- **Staging**: Production-like environment with test data
- **Production**: Optimized build, encrypted storage, monitoring

### Security and Compliance Framework

#### Security Layers

- **Input Validation**: Comprehensive validation at all entry points
- **Output Sanitization**: XSS prevention and content filtering
- **Encryption**: AES-256 encryption for sensitive data
- **Access Control**: Role-based permissions and audit logging
- **Network Security**: HTTPS enforcement and certificate validation

#### Compliance Requirements

- **Data Privacy**: GDPR compliance with data anonymization
- **Audit Trails**: Comprehensive operation logging
- **Backup and Recovery**: Automated backup with encryption
- **Vulnerability Management**: Regular security scanning and updates

### Performance and Monitoring

#### Performance Optimization

- **Code Splitting**: Route and component-based lazy loading
- **Caching Strategy**: Multi-level caching with intelligent invalidation
- **Resource Management**: Memory monitoring and garbage collection
- **Database Optimization**: Query optimization and indexing strategy

#### Monitoring and Observability

- **Application Metrics**: Performance, usage, and error metrics
- **Health Checks**: System health monitoring and alerting
- **User Analytics**: Usage patterns and feature adoption
- **Error Tracking**: Comprehensive error reporting and analysis

This comprehensive project structure supports enterprise-grade development with
clear separation of concerns, robust testing infrastructure, and
production-ready deployment capabilities.
