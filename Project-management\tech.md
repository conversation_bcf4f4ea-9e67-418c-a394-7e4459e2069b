---
type: 'agent_requested'
description: 'Example description'
---

# Technology Stack and Architecture

## Core Framework and Runtime

- **Electron 37.2.5**: Cross-platform desktop application framework with
  security hardening
- **Node.js**: Runtime environment with multi-process architecture and worker
  thread support
- **TypeScript**: Primary development language with strict mode and
  comprehensive type safety
- **JavaScript ES2022+**: Modern JavaScript features with async/await and module
  support

## Frontend Technologies and UI Framework

- **React 18.2.0**: UI component library with concurrent features, Suspense, and
  error boundaries
- **React Router 6.8.0**: Client-side routing with nested routes, code
  splitting, and navigation guards
- **TailwindCSS 4.1.11**: Utility-first CSS framework with custom design tokens
  and responsive design
- **DaisyUI 5.0.50**: Component library with theme customization and
  accessibility features
- **Material Icons 1.13.14**: Comprehensive icon library with high-DPI support

## State Management and Data Flow

- **Zustand 4.4.7**: Lightweight state management with persistence and
  middleware support
- **React Query 5.17.0**: Server state management with caching, background
  updates, and optimistic updates
- **React Hook Form 7.48.2**: Form handling with validation, error management,
  and performance optimization
- **Electron Store 8.1.0**: Persistent user preferences with encryption and
  migration support

## Advanced UI Components and Interactions

- **Monaco Editor 0.45.0**: VSCode editor component with syntax highlighting and
  IntelliSense
- **React DnD 16.0.1**: Drag and drop with HTML5 backend and accessibility
  support
- **React Virtualized 9.22.5**: Efficient rendering of large lists with
  windowing
- **React Resizable Panels 0.0.55**: Resizable layout panels with persistence
- **Framer Motion 10.16.16**: Animation library with physics-based animations
  and gestures
- **React Hot Toast 2.4.1**: Toast notifications with queuing and customization
- **React Hotkeys Hook 4.4.1**: Keyboard shortcuts with conflict detection and
  customization

## Document Processing and Analysis

- **PDF.js 5.4.54**: PDF rendering, parsing, form field extraction, and
  manipulation
- **PDFKit 0.17.1**: PDF generation, form filling, and annotation capabilities
- **PDF-lib 1.17.1**: Advanced PDF manipulation and digital signature support
- **Tesseract.js 6.0.1**: OCR engine with multi-language support and confidence
  scoring
- **Sharp 0.33.2**: High-performance image processing with optimization and
  format conversion
- **Mammoth 1.6.0**: Word document processing with structure and media
  preservation
- **ExcelJS 4.4.0**: Excel file processing with formula evaluation and chart
  extraction
- **CSV Parser 3.0.0**: CSV processing with encoding detection and validation
- **JSZip 3.10.1**: ZIP file handling for document archives and compression
- **File-Type 19.0.0**: File type detection and MIME type validation

## AI and Machine Learning Integration

- **@azure-rest/ai-inference 1.0.0-beta.6**: Azure AI model integration with
  embeddings and completion
- **OpenAI 4.28.0**: GPT model integration with streaming and function calling
- **LangChain 0.1.25**: AI application framework with agents, tools, and memory
  management
- **ChromaDB 3.0.10**: Vector database with semantic search and metadata
  filtering
- **TensorFlow.js 4.15.0**: Client-side machine learning with model inference
- **Natural 6.12.0**: Natural language processing with tokenization and
  sentiment analysis
- **Compromise 14.10.0**: Natural language understanding with entity recognition
- **MathJS 12.3.0**: Mathematical expression evaluation and scientific computing

## Database and Storage Systems

- **Better-SQLite3 12.2.0**: High-performance SQLite with WAL mode and prepared
  statements
- **Knex.js 3.1.0**: SQL query builder with migrations, transactions, and
  connection pooling
- **Node-Cache 5.1.2**: In-memory caching with TTL and automatic cleanup
- **FS-Extra 11.2.0**: Enhanced file system operations with atomic writes and
  error handling

## Security and Encryption

- **Crypto-js 4.2.0**: Cryptographic functions with AES-256 encryption and key
  derivation
- **Joi 17.12.1**: Data validation with comprehensive schema definitions and
  error reporting
- **UUID 9.0.1**: Unique identifier generation with cryptographic randomness

## Networking and Communication

- **Axios 1.6.7**: HTTP client with interceptors, timeout handling, and retry
  logic
- **Socket.io 4.7.4**: Real-time communication for future collaboration features
- **Bull 4.12.2**: Job queue management with Redis backend and retry mechanisms
- **IORedis 5.3.2**: Redis client with cluster support and connection pooling

## Performance and Optimization

- **LZ4 0.6.5**: Fast compression for state storage and data transfer
- **Worker Threads**: CPU-intensive task processing in separate threads
- **Cluster**: Multi-process scaling for document processing workloads

## Development and Build Tools

- **Webpack 5.89.0**: Module bundling with code splitting and optimization
- **Babel 7.23.7**: JavaScript transpilation with TypeScript and React presets
- **ESLint 8.56.0**: Code linting with TypeScript, React, and accessibility
  rules
- **Prettier 3.2.4**: Code formatting with consistent style enforcement
- **Husky 8.0.3**: Git hooks for pre-commit validation and testing
- **Lint-staged 15.2.0**: Staged file processing for efficient CI/CD

## Testing Framework and Quality Assurance

- **Jest 29.7.0**: Testing framework with coverage reporting and parallel
  execution
- **React Testing Library 14.1.2**: Component testing with user-centric approach
- **Playwright 1.41.1**: End-to-end testing with cross-browser support
- **@testing-library/jest-dom 6.2.0**: Custom Jest matchers for DOM testing

## Electron Build and Distribution

- **@electron-forge/cli 7.8.2**: Complete build and packaging solution
- **Electron Builder 24.9.1**: Advanced packaging with auto-updater and code
  signing
- **@electron-forge/maker-squirrel**: Windows installer with auto-update support
- **@electron-forge/maker-zip**: macOS app packaging with notarization
- **@electron-forge/maker-deb**: Debian package creation with dependencies
- **@electron-forge/maker-rpm**: RPM package creation for Linux distributions
- **@electron-forge/plugin-fuses**: Security configuration with runtime
  restrictions

## Development Commands and Scripts

### Development Workflow

```bash
npm start                    # Start development server with hot reload and DevTools
npm run dev                  # Development mode with file watching and auto-restart
npm run build:css           # Compile TailwindCSS with purging and optimization
npm run type-check          # TypeScript type checking without compilation
npm run lint                # ESLint with auto-fix for code quality
npm run format              # Prettier formatting for consistent code style
npm run test                # Jest test suite with coverage reporting
npm run test:watch          # Jest in watch mode for development
npm run test:e2e            # Playwright end-to-end tests
```

### Build and Packaging

```bash
npm run build               # Production build with optimization and minification
npm run package             # Package app for current platform with ASAR
npm run make                # Create distributable packages for all platforms
npm run publish             # Publish to configured distribution channels
npm run dist                # Full distribution build with code signing
```

### Database and Migrations

```bash
npm run db:migrate          # Run database migrations
npm run db:rollback         # Rollback last migration
npm run db:seed             # Seed database with test data
npm run db:reset            # Reset database and run migrations
```

### Quality Assurance

```bash
npm run test:unit           # Unit tests with coverage
npm run test:integration    # Integration tests
npm run test:performance    # Performance benchmarking
npm run test:security       # Security vulnerability scanning
npm run analyze             # Bundle analysis and optimization suggestions
```

## Architecture Patterns and Design Principles

### Multi-Process Architecture

- **Main Process**: Application lifecycle, file system access, database
  operations, AI processing coordination
- **Renderer Process**: React UI, user interactions, state management, IPC
  communication
- **Worker Processes**: CPU-intensive tasks, document processing, AI model
  inference, OCR operations
- **Preload Scripts**: Secure API exposure with context isolation and privilege
  separation

### Security-First Design

- **Content Security Policy**: Strict CSP headers preventing XSS and code
  injection
- **Context Isolation**: Renderer process isolation with secure IPC
  communication
- **Input Validation**: Comprehensive validation using Joi schemas and
  sanitization
- **Encryption**: AES-256 encryption for sensitive data with secure key
  management
- **Audit Logging**: Comprehensive operation logging for security and compliance

### Performance Optimization Strategies

- **Multi-Level Caching**: Memory, SQLite, and file system caching with
  intelligent invalidation
- **Lazy Loading**: Component and data lazy loading with React.lazy and Suspense
- **Virtual Scrolling**: Efficient rendering of large lists with
  react-virtualized
- **Code Splitting**: Route-based and component-based code splitting for faster
  loading
- **Resource Management**: Memory monitoring, cleanup, and garbage collection
  optimization

### Scalability and Maintainability

- **Modular Architecture**: Clear separation of concerns with dependency
  injection
- **Type Safety**: Comprehensive TypeScript coverage with strict mode
- **Error Handling**: Production-grade error boundaries, logging, and recovery
- **Testing Strategy**: Unit, integration, E2E, and performance testing with
  95%+ coverage
- **Documentation**: Comprehensive API documentation with examples and best
  practices

## File Structure and Organization Conventions

### Source Code Organization

```
src/
├── main/                   # Electron main process (Node.js/TypeScript)
├── renderer/               # React frontend (TypeScript/JSX)
├── shared/                 # Shared types and utilities
└── assets/                 # Static assets and resources
```

### Development Workflow Patterns

1. **Feature Development**: Create feature branch → Implement with tests → Code
   review → Merge
2. **Database Changes**: Create migration → Update types → Test rollback →
   Deploy
3. **UI Components**: Design → Implement → Test → Document → Integrate
4. **AI Integration**: Mock → Implement → Test with real APIs → Optimize →
   Monitor
5. **Performance**: Baseline → Implement → Benchmark → Optimize → Validate

### Code Quality Standards

- **TypeScript Strict Mode**: All code must pass strict type checking
- **Test Coverage**: Minimum 95% coverage for critical paths
- **Performance Budgets**: Response times under 100ms for UI interactions
- **Accessibility**: WCAG 2.1 AA compliance with automated testing
- **Security**: All inputs validated, outputs sanitized, secrets encrypted
