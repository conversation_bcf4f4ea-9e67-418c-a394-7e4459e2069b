import { ipcMain, dialog } from 'electron';
import * as fs from 'fs/promises';
import * as path from 'path';
import { watch } from 'chokidar';
import { 
  FileSystemNode, 
  FileNodeType, 
  FileOperation, 
  FileOperationType,
  FileWatchEvent 
} from '../../shared/types/FileExplorer';
import { getFileType, formatFileSize } from '../../shared/utils/fileTypes';

class FileSystemService {
  private watchers = new Map<string, any>();
  private watchCallbacks = new Map<string, (event: FileWatchEvent) => void>();

  /**
   * Read directory contents and return file system nodes
   */
  async readDirectory(dirPath: string): Promise<FileSystemNode[]> {
    try {
      const entries = await fs.readdir(dirPath, { withFileTypes: true });
      const nodes: FileSystemNode[] = [];

      for (const entry of entries) {
        const fullPath = path.join(dirPath, entry.name);
        const stats = await fs.stat(fullPath).catch(() => null);
        
        if (!stats) continue;

        const node: FileSystemNode = {
          id: fullPath,
          name: entry.name,
          path: fullPath,
          type: entry.isDirectory() ? FileNodeType.DIRECTORY : FileNodeType.FILE,
          size: entry.isFile() ? stats.size : undefined,
          lastModified: stats.mtime,
          isExpanded: false,
          isSelected: false,
          isLoading: false,
          children: entry.isDirectory() ? [] : undefined,
          depth: 0,
          extension: entry.isFile() ? path.extname(entry.name) : undefined,
          isHidden: entry.name.startsWith('.'),
          permissions: {
            readable: true, // We'll assume readable for now
            writable: true, // We'll assume writable for now
            executable: entry.isDirectory() || path.extname(entry.name) === '.exe'
          },
          metadata: entry.isFile() ? {
            mimeType: getFileType(entry.name).mimeTypes?.[0],
            isText: getFileType(entry.name).isText,
            isBinary: getFileType(entry.name).isBinary,
            isImage: getFileType(entry.name).category === 'image',
            isDocument: getFileType(entry.name).category === 'document'
          } : undefined
        };

        // Set icon based on file type
        if (entry.isDirectory()) {
          node.icon = 'folder';
        } else {
          const fileType = getFileType(entry.name);
          node.icon = fileType.icon;
        }

        nodes.push(node);
      }

      // Sort nodes: directories first, then files, both alphabetically
      return nodes.sort((a, b) => {
        if (a.type !== b.type) {
          return a.type === FileNodeType.DIRECTORY ? -1 : 1;
        }
        return a.name.localeCompare(b.name, undefined, { numeric: true });
      });

    } catch (error) {
      console.error('Error reading directory:', error);
      throw new Error(`Failed to read directory: ${error.message}`);
    }
  }

  /**
   * Get file or directory information
   */
  async getFileInfo(filePath: string): Promise<FileSystemNode | null> {
    try {
      const stats = await fs.stat(filePath);
      const name = path.basename(filePath);
      const isDirectory = stats.isDirectory();

      const node: FileSystemNode = {
        id: filePath,
        name,
        path: filePath,
        type: isDirectory ? FileNodeType.DIRECTORY : FileNodeType.FILE,
        size: isDirectory ? undefined : stats.size,
        lastModified: stats.mtime,
        isExpanded: false,
        isSelected: false,
        isLoading: false,
        children: isDirectory ? [] : undefined,
        depth: 0,
        extension: isDirectory ? undefined : path.extname(name),
        isHidden: name.startsWith('.'),
        permissions: {
          readable: true,
          writable: true,
          executable: isDirectory || path.extname(name) === '.exe'
        }
      };

      if (isDirectory) {
        node.icon = 'folder';
      } else {
        const fileType = getFileType(name);
        node.icon = fileType.icon;
        node.metadata = {
          mimeType: fileType.mimeTypes?.[0],
          isText: fileType.isText,
          isBinary: fileType.isBinary,
          isImage: fileType.category === 'image',
          isDocument: fileType.category === 'document'
        };
      }

      return node;
    } catch (error) {
      console.error('Error getting file info:', error);
      return null;
    }
  }

  /**
   * Perform file operations
   */
  async performOperation(operation: FileOperation): Promise<boolean> {
    try {
      switch (operation.type) {
        case FileOperationType.CREATE_FILE:
          await fs.writeFile(operation.source, '');
          break;

        case FileOperationType.CREATE_FOLDER:
          await fs.mkdir(operation.source, { recursive: true });
          break;

        case FileOperationType.DELETE:
          const stats = await fs.stat(operation.source);
          if (stats.isDirectory()) {
            await fs.rmdir(operation.source, { recursive: true });
          } else {
            await fs.unlink(operation.source);
          }
          break;

        case FileOperationType.RENAME:
          if (!operation.target) throw new Error('Target path required for rename');
          await fs.rename(operation.source, operation.target);
          break;

        case FileOperationType.MOVE:
          if (!operation.target) throw new Error('Target path required for move');
          await fs.rename(operation.source, operation.target);
          break;

        case FileOperationType.COPY:
          if (!operation.target) throw new Error('Target path required for copy');
          await this.copyRecursive(operation.source, operation.target);
          break;

        default:
          throw new Error(`Unsupported operation: ${operation.type}`);
      }

      return true;
    } catch (error) {
      console.error('Error performing file operation:', error);
      throw new Error(`Operation failed: ${error.message}`);
    }
  }

  /**
   * Recursively copy files and directories
   */
  private async copyRecursive(source: string, target: string): Promise<void> {
    const stats = await fs.stat(source);

    if (stats.isDirectory()) {
      await fs.mkdir(target, { recursive: true });
      const entries = await fs.readdir(source);
      
      for (const entry of entries) {
        const sourcePath = path.join(source, entry);
        const targetPath = path.join(target, entry);
        await this.copyRecursive(sourcePath, targetPath);
      }
    } else {
      await fs.copyFile(source, target);
    }
  }

  /**
   * Start watching a directory for changes
   */
  startWatching(dirPath: string, callback: (event: FileWatchEvent) => void): void {
    if (this.watchers.has(dirPath)) {
      this.stopWatching(dirPath);
    }

    const watcher = watch(dirPath, {
      ignored: /(^|[\/\\])\../, // ignore dotfiles
      persistent: true,
      ignoreInitial: true
    });

    watcher
      .on('add', (filePath) => {
        callback({
          type: 'created',
          path: filePath,
          timestamp: new Date()
        });
      })
      .on('change', (filePath) => {
        callback({
          type: 'modified',
          path: filePath,
          timestamp: new Date()
        });
      })
      .on('unlink', (filePath) => {
        callback({
          type: 'deleted',
          path: filePath,
          timestamp: new Date()
        });
      })
      .on('addDir', (dirPath) => {
        callback({
          type: 'created',
          path: dirPath,
          timestamp: new Date()
        });
      })
      .on('unlinkDir', (dirPath) => {
        callback({
          type: 'deleted',
          path: dirPath,
          timestamp: new Date()
        });
      });

    this.watchers.set(dirPath, watcher);
    this.watchCallbacks.set(dirPath, callback);
  }

  /**
   * Stop watching a directory
   */
  stopWatching(dirPath: string): void {
    const watcher = this.watchers.get(dirPath);
    if (watcher) {
      watcher.close();
      this.watchers.delete(dirPath);
      this.watchCallbacks.delete(dirPath);
    }
  }

  /**
   * Show native file dialog
   */
  async showOpenDialog(options: any = {}): Promise<string[]> {
    const result = await dialog.showOpenDialog({
      properties: ['openFile', 'openDirectory', 'multiSelections'],
      ...options
    });

    return result.canceled ? [] : result.filePaths;
  }

  /**
   * Show native save dialog
   */
  async showSaveDialog(options: any = {}): Promise<string | null> {
    const result = await dialog.showSaveDialog(options);
    return result.canceled ? null : result.filePath || null;
  }
}

// Create service instance
const fileSystemService = new FileSystemService();

// Register IPC handlers
export function registerFileSystemHandlers(): void {
  // Directory operations
  ipcMain.handle('fs:read-directory', async (_, dirPath: string) => {
    return await fileSystemService.readDirectory(dirPath);
  });

  ipcMain.handle('fs:get-file-info', async (_, filePath: string) => {
    return await fileSystemService.getFileInfo(filePath);
  });

  // File operations
  ipcMain.handle('fs:perform-operation', async (_, operation: FileOperation) => {
    return await fileSystemService.performOperation(operation);
  });

  // File dialogs
  ipcMain.handle('fs:show-open-dialog', async (_, options) => {
    return await fileSystemService.showOpenDialog(options);
  });

  ipcMain.handle('fs:show-save-dialog', async (_, options) => {
    return await fileSystemService.showSaveDialog(options);
  });

  // File watching
  ipcMain.handle('fs:start-watching', (event, dirPath: string) => {
    fileSystemService.startWatching(dirPath, (watchEvent) => {
      event.sender.send('fs:file-changed', watchEvent);
    });
  });

  ipcMain.handle('fs:stop-watching', (_, dirPath: string) => {
    fileSystemService.stopWatching(dirPath);
  });
}

export { fileSystemService };
