import { contextBridge, ipc<PERSON><PERSON>er } from 'electron';

// Define the API that will be exposed to the renderer process
export interface ElectronAPI {
  // File operations
  openFile: () => Promise<string | null>;
  saveFile: (content: string, filePath?: string) => Promise<boolean>;

  // Document processing
  processDocument: (filePath: string) => Promise<any>;

  // AI operations
  generateEmbeddings: (text: string) => Promise<number[]>;
  performReasoning: (context: string, query: string) => Promise<string>;

  // Knowledge base operations
  storeInformation: (data: any) => Promise<void>;
  queryInformation: (query: string) => Promise<any[]>;

  // File system operations
  'fs:read-directory': (dirPath: string) => Promise<any[]>;
  'fs:get-file-info': (filePath: string) => Promise<any>;
  'fs:perform-operation': (operation: any) => Promise<boolean>;
  'fs:show-open-dialog': (options?: any) => Promise<string[]>;
  'fs:show-save-dialog': (options?: any) => Promise<string | null>;
  'fs:start-watching': (dirPath: string) => Promise<void>;
  'fs:stop-watching': (dirPath: string) => Promise<void>;

  // Event system
  on: (channel: string, callback: (...args: any[]) => void) => void;
  off: (channel: string, callback: (...args: any[]) => void) => void;

  // Window controls
  windowControls: {
    minimize: () => Promise<void>;
    maximize: () => Promise<void>;
    close: () => Promise<void>;
    isMaximized: () => Promise<boolean>;
    setFullscreen: (fullscreen: boolean) => Promise<void>;
    onStateChange: (callback: () => void) => void;
    removeStateListener: (callback: () => void) => void;
  };

  // Timeline operations
  timeline: {
    getTimeline: (query?: any) => Promise<any>;
    getBranches: () => Promise<any>;
    getUndoRedoState: () => Promise<any>;
    createCheckpoint: (state: any, description: string) => Promise<string>;
    restoreCheckpoint: (checkpointId: string) => Promise<any>;
    undo: () => Promise<any>;
    redo: () => Promise<any>;
    createBranch: (fromCheckpoint: string, name: string, description?: string) => Promise<any>;
    switchBranch: (branchId: string) => Promise<any>;
    mergeBranches: (sourceBranch: string, targetBranch: string) => Promise<any>;
    createDiff: (before: any, after: any) => Promise<any>;
    createVisualDiff: (before: any, after: any) => Promise<any>;
    getDocumentHistory: (documentId: string) => Promise<any>;
    onTimelineUpdate?: (callback: (data: any) => void) => void;
    offTimelineUpdate?: (callback: (data: any) => void) => void;
  };
  app: {
    getCurrentState: () => Promise<any>;
  };

  // System operations
  getAppVersion: () => Promise<string>;
  showMessageBox: (options: any) => Promise<any>;

  // Event listeners
  onDocumentProcessed: (callback: (data: any) => void) => void;
  onAIResponse: (callback: (response: string) => void) => void;
  removeAllListeners: (channel: string) => void;

  // Logging operations
  logToMain: (level: string, message: string, data?: any, stack?: string) => Promise<void>;
}

// Expose protected methods that allow the renderer process to use
// the ipcRenderer without exposing the entire object
const electronAPI: ElectronAPI = {
  // File operations
  openFile: () => ipcRenderer.invoke('file:open'),
  saveFile: (content: string, filePath?: string) =>
    ipcRenderer.invoke('file:save', content, filePath),

  // Document processing
  processDocument: (filePath: string) => ipcRenderer.invoke('document:process', filePath),

  // AI operations
  generateEmbeddings: (text: string) => ipcRenderer.invoke('ai:generate-embeddings', text),
  performReasoning: (context: string, query: string) =>
    ipcRenderer.invoke('ai:perform-reasoning', context, query),

  // Knowledge base operations
  storeInformation: (data: any) => ipcRenderer.invoke('knowledge:store', data),
  queryInformation: (query: string) => ipcRenderer.invoke('knowledge:query', query),

  // File system operations
  'fs:read-directory': (dirPath: string) => ipcRenderer.invoke('fs:read-directory', dirPath),
  'fs:get-file-info': (filePath: string) => ipcRenderer.invoke('fs:get-file-info', filePath),
  'fs:perform-operation': (operation: any) => ipcRenderer.invoke('fs:perform-operation', operation),
  'fs:show-open-dialog': (options?: any) => ipcRenderer.invoke('fs:show-open-dialog', options),
  'fs:show-save-dialog': (options?: any) => ipcRenderer.invoke('fs:show-save-dialog', options),
  'fs:start-watching': (dirPath: string) => ipcRenderer.invoke('fs:start-watching', dirPath),
  'fs:stop-watching': (dirPath: string) => ipcRenderer.invoke('fs:stop-watching', dirPath),

  // Event system
  on: (channel: string, callback: (...args: any[]) => void) => {
    ipcRenderer.on(channel, (_event, ...args) => callback(...args));
  },
  off: (channel: string, callback: (...args: any[]) => void) => {
    ipcRenderer.removeListener(channel, callback);
  },

  // Timeline operations
  timeline: {
    getTimeline: (query?: any) => ipcRenderer.invoke('timeline:get-timeline', query),
    getBranches: () => ipcRenderer.invoke('timeline:get-branches'),
    getUndoRedoState: () => ipcRenderer.invoke('timeline:get-undo-redo-state'),
    createCheckpoint: (state: any, description: string) =>
      ipcRenderer.invoke('timeline:create-checkpoint', state, description),
    restoreCheckpoint: (checkpointId: string) =>
      ipcRenderer.invoke('timeline:restore-checkpoint', checkpointId),
    undo: () => ipcRenderer.invoke('timeline:undo'),
    redo: () => ipcRenderer.invoke('timeline:redo'),
    createBranch: (fromCheckpoint: string, name: string, description?: string) =>
      ipcRenderer.invoke('timeline:create-branch', fromCheckpoint, name, description),
    switchBranch: (branchId: string) => ipcRenderer.invoke('timeline:switch-branch', branchId),
    mergeBranches: (sourceBranch: string, targetBranch: string) =>
      ipcRenderer.invoke('timeline:merge-branches', sourceBranch, targetBranch),
    createDiff: (before: any, after: any) =>
      ipcRenderer.invoke('timeline:create-diff', before, after),
    createVisualDiff: (before: any, after: any) =>
      ipcRenderer.invoke('timeline:create-visual-diff', before, after),
    getDocumentHistory: (documentId: string) =>
      ipcRenderer.invoke('timeline:get-document-history', documentId),
    onTimelineUpdate: (callback: (data: any) => void) => {
      ipcRenderer.on('timeline:update', (_event, data) => callback(data));
    },
    offTimelineUpdate: (callback: (data: any) => void) => {
      ipcRenderer.removeListener('timeline:update', callback);
    },
  },
  app: {
    getCurrentState: () => ipcRenderer.invoke('app:get-current-state'),
  },

  // Window controls
  windowControls: {
    minimize: () => ipcRenderer.invoke('window:minimize'),
    maximize: () => ipcRenderer.invoke('window:maximize'),
    close: () => ipcRenderer.invoke('window:close'),
    isMaximized: () => ipcRenderer.invoke('window:is-maximized'),
    setFullscreen: (fullscreen: boolean) => ipcRenderer.invoke('window:set-fullscreen', fullscreen),
    onStateChange: (callback: () => void) => {
      ipcRenderer.on('window:state-changed', callback);
    },
    removeStateListener: (callback: () => void) => {
      ipcRenderer.removeListener('window:state-changed', callback);
    },
  },

  // System operations
  getAppVersion: () => ipcRenderer.invoke('app:get-version'),
  showMessageBox: (options: any) => ipcRenderer.invoke('app:show-message-box', options),

  // Event listeners
  onDocumentProcessed: (callback: (data: any) => void) => {
    ipcRenderer.on('document:processed', (_event, data) => callback(data));
  },
  onAIResponse: (callback: (response: string) => void) => {
    ipcRenderer.on('ai:response', (_event, response) => callback(response));
  },
  removeAllListeners: (channel: string) => {
    ipcRenderer.removeAllListeners(channel);
  },

  // Logging operations
  logToMain: (level: string, message: string, data?: any, stack?: string) =>
    ipcRenderer.invoke('log:fromRenderer', level, message, data, stack),
};

// Use `contextBridge` APIs to expose Electron APIs to
// renderer only if context isolation is enabled, otherwise
// just add to the DOM global.
if (process.contextIsolated) {
  try {
    contextBridge.exposeInMainWorld('electronAPI', electronAPI);
  } catch (error) {
    console.error('Failed to expose electronAPI:', error);
  }
} else {
  // @ts-ignore - window is available in non-isolated context
  window.electronAPI = electronAPI;
}

// Expose environment information
contextBridge.exposeInMainWorld('electronEnv', {
  NODE_ENV: process.env.NODE_ENV,
  platform: process.platform,
  arch: process.arch,
  versions: process.versions,
});

// Security: Remove Node.js globals from renderer process
// Only attempt to delete these if context isolation is disabled
if (!process.contextIsolated) {
  const globalWindow = globalThis as any;
  delete globalWindow.require;
  delete globalWindow.exports;
  delete globalWindow.module;
}

// Log preload script loaded
console.log('Preload script loaded successfully');
