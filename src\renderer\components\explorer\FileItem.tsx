import React, { useCallback, useState } from 'react';
import { motion } from 'framer-motion';
import { FileSystemNode, FileNodeType } from '../../../shared/types/FileExplorer';
import {
  getFileIcon,
  getFileColor,
  formatFileSize,
  formatDate,
} from '../../../shared/utils/fileTypes';
import { useDragNode, useDropNode } from '../../hooks/useDragDrop';
import { useSelectedNodes } from '../../stores/fileExplorerStore';

interface FileItemProps {
  node: FileSystemNode;
  level: number;
  isSelected: boolean;
  isExpanded: boolean;
  hasChildren: boolean;
  onExpand: () => void;
  onCollapse: () => void;
  onSelect: (multiSelect: boolean) => void;
  onDoubleClick: () => void;
  onRightClick: (event: React.MouseEvent) => void;
  showSize?: boolean;
  showModified?: boolean;
  isRenaming?: boolean;
  onRename?: (newName: string) => void;
}

const INDENT_SIZE = 16;

export const FileItem: React.FC<FileItemProps> = ({
  node,
  level,
  isSelected,
  isExpanded,
  hasChildren,
  onExpand,
  onCollapse,
  onSelect,
  onDoubleClick,
  onRightClick,
  showSize = false,
  showModified = false,
  isRenaming = false,
  onRename,
}) => {
  const [isHovered, setIsHovered] = useState(false);
  const [editingName, setEditingName] = useState(node.name);

  const isDirectory = node.type === FileNodeType.DIRECTORY;
  const icon = getFileIcon(node.name, isDirectory);
  const color = getFileColor(node.name, isDirectory);

  // Drag and drop hooks
  const selectedNodes = useSelectedNodes();
  const { isDragging, drag, dragPreview } = useDragNode(node, selectedNodes);
  const { isOver, canDrop, drop } = useDropNode(node);

  // Handle expand/collapse toggle
  const handleToggleExpand = useCallback(
    (event: React.MouseEvent) => {
      event.stopPropagation();
      if (isExpanded) {
        onCollapse();
      } else {
        onExpand();
      }
    },
    [isExpanded, onExpand, onCollapse]
  );

  // Handle item click
  const handleClick = useCallback(
    (event: React.MouseEvent) => {
      const multiSelect = event.ctrlKey || event.metaKey;
      onSelect(multiSelect);
    },
    [onSelect]
  );

  // Handle double click
  const handleDoubleClick = useCallback(
    (event: React.MouseEvent) => {
      event.stopPropagation();
      onDoubleClick();
    },
    [onDoubleClick]
  );

  // Handle right click
  const handleRightClick = useCallback(
    (event: React.MouseEvent) => {
      event.preventDefault();
      event.stopPropagation();
      onRightClick(event);
    },
    [onRightClick]
  );

  // Handle rename
  const handleRename = useCallback(
    (event: React.KeyboardEvent<HTMLInputElement>) => {
      if (event.key === 'Enter') {
        onRename?.(editingName);
      } else if (event.key === 'Escape') {
        setEditingName(node.name);
        onRename?.(node.name); // Cancel rename
      }
    },
    [editingName, node.name, onRename]
  );

  // Handle rename blur
  const handleRenameBlur = useCallback(() => {
    onRename?.(editingName);
  }, [editingName, onRename]);

  const paddingLeft = level * INDENT_SIZE + 8;

  return (
    <motion.div
      ref={el => {
        drag(el);
        drop(el);
        dragPreview(el);
      }}
      className={`
        file-item flex items-center w-full h-6 px-1 cursor-pointer select-none
        ${isSelected ? 'bg-primary text-primary-content' : 'hover:bg-base-200'}
        ${node.isHidden ? 'opacity-60' : ''}
        ${isDragging ? 'opacity-50' : ''}
        ${isOver && canDrop ? 'bg-accent/20 border-l-2 border-accent' : ''}
        ${isOver && !canDrop ? 'bg-error/20' : ''}
      `}
      style={{ paddingLeft }}
      onClick={handleClick}
      onDoubleClick={handleDoubleClick}
      onContextMenu={handleRightClick}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      initial={{ opacity: 0, x: -10 }}
      animate={{ opacity: 1, x: 0 }}
      transition={{ duration: 0.15 }}
      whileHover={{ backgroundColor: isSelected ? undefined : 'rgba(0,0,0,0.05)' }}
    >
      {/* Expand/Collapse Arrow */}
      <div className='flex items-center justify-center w-4 h-4 mr-1'>
        {isDirectory && hasChildren && (
          <motion.button
            className='flex items-center justify-center w-4 h-4 rounded hover:bg-base-300'
            onClick={handleToggleExpand}
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
          >
            <motion.span
              className='material-icons text-xs'
              animate={{ rotate: isExpanded ? 90 : 0 }}
              transition={{ duration: 0.2 }}
            >
              chevron_right
            </motion.span>
          </motion.button>
        )}
      </div>

      {/* File/Folder Icon */}
      <div className='flex items-center justify-center w-4 h-4 mr-2'>
        <span className='material-icons text-sm' style={{ color: isSelected ? 'inherit' : color }}>
          {icon}
        </span>
      </div>

      {/* File/Folder Name */}
      <div className='flex-1 min-w-0'>
        {isRenaming ? (
          <input
            type='text'
            value={editingName}
            onChange={e => setEditingName(e.target.value)}
            onKeyDown={handleRename}
            onBlur={handleRenameBlur}
            className='input input-xs w-full bg-base-100 text-base-content'
            autoFocus
            onClick={e => e.stopPropagation()}
          />
        ) : (
          <span className='text-xs truncate block' title={node.name}>
            {node.name}
          </span>
        )}
      </div>

      {/* File Size */}
      {showSize && !isDirectory && node.size !== undefined && (
        <div className='text-xs text-base-content/60 ml-2 min-w-0'>{formatFileSize(node.size)}</div>
      )}

      {/* Last Modified */}
      {showModified && node.lastModified && (
        <div className='text-xs text-base-content/60 ml-2 min-w-0'>
          {formatDate(node.lastModified)}
        </div>
      )}

      {/* Loading Indicator */}
      {node.isLoading && (
        <div className='ml-2'>
          <span className='loading loading-spinner loading-xs'></span>
        </div>
      )}

      {/* Status Indicators */}
      <div className='flex items-center ml-1'>
        {/* Hidden file indicator */}
        {node.isHidden && (
          <span className='material-icons text-xs opacity-50' title='Hidden file'>
            visibility_off
          </span>
        )}

        {/* Read-only indicator */}
        {node.permissions && !node.permissions.writable && (
          <span className='material-icons text-xs text-warning ml-1' title='Read-only'>
            lock
          </span>
        )}

        {/* Symlink indicator */}
        {node.type === FileNodeType.SYMLINK && (
          <span className='material-icons text-xs text-info ml-1' title='Symbolic link'>
            link
          </span>
        )}
      </div>
    </motion.div>
  );
};

export default FileItem;
