import React, { useState, useEffect, useCallback, useMemo, useRef } from 'react';
import { FixedSizeList as List } from 'react-window';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  FileSystemNode, 
  FileExplorerState, 
  FileSortBy, 
  SortOrder,
  FileNodeType 
} from '../../../shared/types/FileExplorer';
import { fileSystemService } from '../../services/fileSystemService';
import { getFileIcon, getFileColor, formatFileSize, formatDate } from '../../../shared/utils/fileTypes';
import FileItem from './FileItem';

interface FileTreeProps {
  rootPath: string;
  height: number;
  width?: number;
  onNodeSelect?: (node: FileSystemNode) => void;
  onNodeDoubleClick?: (node: FileSystemNode) => void;
  onNodeRightClick?: (node: FileSystemNode, event: React.MouseEvent) => void;
  showHiddenFiles?: boolean;
  sortBy?: FileSortBy;
  sortOrder?: SortOrder;
  className?: string;
}

interface TreeNode extends FileSystemNode {
  level: number;
  isVisible: boolean;
  hasChildren: boolean;
}

const ITEM_HEIGHT = 24;
const INDENT_SIZE = 16;

export const FileTree: React.FC<FileTreeProps> = ({
  rootPath,
  height,
  width = 300,
  onNodeSelect,
  onNodeDoubleClick,
  onNodeRightClick,
  showHiddenFiles = false,
  sortBy = FileSortBy.NAME,
  sortOrder = SortOrder.ASC,
  className = ''
}) => {
  const [nodes, setNodes] = useState<Map<string, FileSystemNode>>(new Map());
  const [expandedNodes, setExpandedNodes] = useState<Set<string>>(new Set());
  const [selectedNodes, setSelectedNodes] = useState<Set<string>>(new Set());
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const listRef = useRef<List>(null);

  // Flatten tree structure for virtualization
  const flattenedNodes = useMemo(() => {
    const result: TreeNode[] = [];
    
    const traverse = (nodeId: string, level: number = 0): void => {
      const node = nodes.get(nodeId);
      if (!node) return;

      // Skip hidden files if not showing them
      if (!showHiddenFiles && node.isHidden) return;

      const treeNode: TreeNode = {
        ...node,
        level,
        isVisible: true,
        hasChildren: node.type === FileNodeType.DIRECTORY && (node.children?.length || 0) > 0
      };

      result.push(treeNode);

      // Add children if expanded
      if (node.type === FileNodeType.DIRECTORY && expandedNodes.has(nodeId) && node.children) {
        const sortedChildren = sortNodes(node.children);
        sortedChildren.forEach(childId => traverse(childId, level + 1));
      }
    };

    // Start with root directory
    const rootNode = nodes.get(rootPath);
    if (rootNode && rootNode.children) {
      const sortedChildren = sortNodes(rootNode.children);
      sortedChildren.forEach(childId => traverse(childId, 0));
    }

    return result;
  }, [nodes, expandedNodes, showHiddenFiles, sortBy, sortOrder, rootPath]);

  // Sort nodes based on current sort criteria
  const sortNodes = useCallback((nodeIds: string[]): string[] => {
    return [...nodeIds].sort((aId, bId) => {
      const a = nodes.get(aId);
      const b = nodes.get(bId);
      if (!a || !b) return 0;

      // Always put directories first
      if (a.type !== b.type) {
        return a.type === FileNodeType.DIRECTORY ? -1 : 1;
      }

      let comparison = 0;
      switch (sortBy) {
        case FileSortBy.NAME:
          comparison = a.name.localeCompare(b.name, undefined, { numeric: true });
          break;
        case FileSortBy.SIZE:
          comparison = (a.size || 0) - (b.size || 0);
          break;
        case FileSortBy.TYPE:
          comparison = (a.extension || '').localeCompare(b.extension || '');
          break;
        case FileSortBy.MODIFIED:
          const aTime = a.lastModified?.getTime() || 0;
          const bTime = b.lastModified?.getTime() || 0;
          comparison = aTime - bTime;
          break;
        default:
          comparison = a.name.localeCompare(b.name);
      }

      return sortOrder === SortOrder.ASC ? comparison : -comparison;
    });
  }, [nodes, sortBy, sortOrder]);

  // Load directory contents
  const loadDirectory = useCallback(async (dirPath: string): Promise<void> => {
    try {
      setLoading(true);
      setError(null);

      const children = await fileSystemService.readDirectory(dirPath);
      
      setNodes(prev => {
        const newNodes = new Map(prev);
        
        // Update or create parent node
        const parentNode = newNodes.get(dirPath) || {
          id: dirPath,
          name: fileSystemService.getFileName(dirPath) || 'Root',
          path: dirPath,
          type: FileNodeType.DIRECTORY,
          depth: 0,
          isExpanded: false,
          isSelected: false,
          isLoading: false,
          children: [],
          icon: 'folder'
        };

        parentNode.children = children.map(child => child.id);
        parentNode.isLoading = false;
        newNodes.set(dirPath, parentNode);

        // Add all children to nodes map
        children.forEach(child => {
          newNodes.set(child.id, child);
        });

        return newNodes;
      });
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load directory');
      console.error('Error loading directory:', err);
    } finally {
      setLoading(false);
    }
  }, []);

  // Handle node expansion
  const handleNodeExpand = useCallback(async (nodeId: string): Promise<void> => {
    const node = nodes.get(nodeId);
    if (!node || node.type !== FileNodeType.DIRECTORY) return;

    if (!expandedNodes.has(nodeId)) {
      setExpandedNodes(prev => new Set([...prev, nodeId]));
      
      // Load children if not already loaded
      if (!node.children || node.children.length === 0) {
        await loadDirectory(nodeId);
      }
    }
  }, [nodes, expandedNodes, loadDirectory]);

  // Handle node collapse
  const handleNodeCollapse = useCallback((nodeId: string): void => {
    setExpandedNodes(prev => {
      const newSet = new Set(prev);
      newSet.delete(nodeId);
      return newSet;
    });
  }, []);

  // Handle node selection
  const handleNodeSelect = useCallback((nodeId: string, multiSelect: boolean = false): void => {
    const node = nodes.get(nodeId);
    if (!node) return;

    setSelectedNodes(prev => {
      if (multiSelect) {
        const newSet = new Set(prev);
        if (newSet.has(nodeId)) {
          newSet.delete(nodeId);
        } else {
          newSet.add(nodeId);
        }
        return newSet;
      } else {
        return new Set([nodeId]);
      }
    });

    onNodeSelect?.(node);
  }, [nodes, onNodeSelect]);

  // Handle node double click
  const handleNodeDoubleClick = useCallback((nodeId: string): void => {
    const node = nodes.get(nodeId);
    if (!node) return;

    if (node.type === FileNodeType.DIRECTORY) {
      if (expandedNodes.has(nodeId)) {
        handleNodeCollapse(nodeId);
      } else {
        handleNodeExpand(nodeId);
      }
    }

    onNodeDoubleClick?.(node);
  }, [nodes, expandedNodes, handleNodeExpand, handleNodeCollapse, onNodeDoubleClick]);

  // Handle node right click
  const handleNodeRightClick = useCallback((nodeId: string, event: React.MouseEvent): void => {
    const node = nodes.get(nodeId);
    if (!node) return;

    // Select the node if not already selected
    if (!selectedNodes.has(nodeId)) {
      handleNodeSelect(nodeId);
    }

    onNodeRightClick?.(node, event);
  }, [nodes, selectedNodes, handleNodeSelect, onNodeRightClick]);

  // Initialize root directory
  useEffect(() => {
    if (rootPath) {
      loadDirectory(rootPath);
    }
  }, [rootPath, loadDirectory]);

  // Render individual tree item
  const renderItem = useCallback(({ index, style }: { index: number; style: React.CSSProperties }) => {
    const treeNode = flattenedNodes[index];
    if (!treeNode) return null;

    return (
      <div style={style}>
        <FileItem
          node={treeNode}
          level={treeNode.level}
          isSelected={selectedNodes.has(treeNode.id)}
          isExpanded={expandedNodes.has(treeNode.id)}
          hasChildren={treeNode.hasChildren}
          onExpand={() => handleNodeExpand(treeNode.id)}
          onCollapse={() => handleNodeCollapse(treeNode.id)}
          onSelect={(multiSelect) => handleNodeSelect(treeNode.id, multiSelect)}
          onDoubleClick={() => handleNodeDoubleClick(treeNode.id)}
          onRightClick={(event) => handleNodeRightClick(treeNode.id, event)}
        />
      </div>
    );
  }, [
    flattenedNodes,
    selectedNodes,
    expandedNodes,
    handleNodeExpand,
    handleNodeCollapse,
    handleNodeSelect,
    handleNodeDoubleClick,
    handleNodeRightClick
  ]);

  if (error) {
    return (
      <div className={`flex items-center justify-center h-full ${className}`}>
        <div className="text-center">
          <span className="material-icons text-error text-4xl mb-2">error</span>
          <p className="text-error text-sm">{error}</p>
          <button 
            className="btn btn-sm btn-outline mt-2"
            onClick={() => loadDirectory(rootPath)}
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className={`file-tree ${className}`}>
      {loading && (
        <div className="flex items-center justify-center p-4">
          <span className="loading loading-spinner loading-sm"></span>
          <span className="ml-2 text-sm">Loading...</span>
        </div>
      )}
      
      {flattenedNodes.length > 0 && (
        <List
          ref={listRef}
          height={height}
          width={width}
          itemCount={flattenedNodes.length}
          itemSize={ITEM_HEIGHT}
          overscanCount={5}
        >
          {renderItem}
        </List>
      )}

      {!loading && flattenedNodes.length === 0 && (
        <div className="flex items-center justify-center h-full text-base-content/50">
          <div className="text-center">
            <span className="material-icons text-4xl mb-2">folder_open</span>
            <p className="text-sm">No files found</p>
          </div>
        </div>
      )}
    </div>
  );
};

export default FileTree;
