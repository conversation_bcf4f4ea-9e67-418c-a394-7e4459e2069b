import { useDrag, useDrop } from 'react-dnd';
import { useCallback } from 'react';
import { 
  FileSystemNode, 
  FileDragDropData, 
  FileOperation, 
  FileOperationType 
} from '../../shared/types/FileExplorer';
import { useFileExplorerStore } from '../stores/fileExplorerStore';

// Drag and drop item types
export const DragDropTypes = {
  FILE_NODE: 'file_node'
} as const;

// Drag item interface
interface DragItem {
  type: string;
  nodeIds: string[];
  nodes: FileSystemNode[];
  operation: 'move' | 'copy';
}

// Drop result interface
interface DropResult {
  targetNode: FileSystemNode;
  operation: 'move' | 'copy';
}

/**
 * Hook for making file nodes draggable
 */
export const useDragNode = (node: FileSystemNode, selectedNodes: FileSystemNode[]) => {
  const performOperation = useFileExplorerStore(state => state.performOperation);

  const [{ isDragging }, drag, dragPreview] = useDrag({
    type: DragDropTypes.FILE_NODE,
    item: (): DragItem => {
      // If the current node is selected, drag all selected nodes
      // Otherwise, just drag the current node
      const nodesToDrag = selectedNodes.some(n => n.id === node.id) 
        ? selectedNodes 
        : [node];

      return {
        type: DragDropTypes.FILE_NODE,
        nodeIds: nodesToDrag.map(n => n.id),
        nodes: nodesToDrag,
        operation: 'move' // Default to move, can be changed with modifier keys
      };
    },
    collect: (monitor) => ({
      isDragging: monitor.isDragging()
    }),
    end: (item, monitor) => {
      const dropResult = monitor.getDropResult<DropResult>();
      if (!dropResult) return;

      // Perform the file operation
      handleDrop(item, dropResult, performOperation);
    }
  });

  return {
    isDragging,
    drag,
    dragPreview
  };
};

/**
 * Hook for making file nodes droppable
 */
export const useDropNode = (node: FileSystemNode) => {
  const [{ isOver, canDrop }, drop] = useDrop({
    accept: DragDropTypes.FILE_NODE,
    drop: (item: DragItem): DropResult => {
      return {
        targetNode: node,
        operation: item.operation
      };
    },
    canDrop: (item: DragItem) => {
      // Can only drop on directories
      if (node.type !== 'directory') return false;

      // Can't drop a node on itself or its children
      return !item.nodeIds.includes(node.id) && !isChildOf(node.id, item.nodes);
    },
    collect: (monitor) => ({
      isOver: monitor.isOver(),
      canDrop: monitor.canDrop()
    })
  });

  return {
    isOver,
    canDrop,
    drop
  };
};

/**
 * Handle the actual drop operation
 */
const handleDrop = async (
  dragItem: DragItem, 
  dropResult: DropResult, 
  performOperation: (operation: FileOperation) => Promise<boolean>
) => {
  const { targetNode, operation } = dropResult;

  for (const sourceNode of dragItem.nodes) {
    const targetPath = `${targetNode.path}/${sourceNode.name}`;

    const fileOperation: FileOperation = {
      type: operation === 'copy' ? FileOperationType.COPY : FileOperationType.MOVE,
      source: sourceNode.path,
      target: targetPath,
      options: {
        overwrite: false,
        recursive: true
      }
    };

    try {
      await performOperation(fileOperation);
    } catch (error) {
      console.error(`Failed to ${operation} ${sourceNode.name}:`, error);
      // You might want to show a toast notification here
    }
  }
};

/**
 * Check if a node is a child of any of the given nodes
 */
const isChildOf = (nodeId: string, parentNodes: FileSystemNode[]): boolean => {
  return parentNodes.some(parent => nodeId.startsWith(parent.path + '/'));
};

/**
 * Hook for handling keyboard modifiers during drag
 */
export const useDragModifiers = () => {
  const handleKeyDown = useCallback((event: KeyboardEvent) => {
    // You can implement modifier key handling here
    // For example, Ctrl+drag for copy, Alt+drag for link, etc.
  }, []);

  const handleKeyUp = useCallback((event: KeyboardEvent) => {
    // Handle key up events
  }, []);

  return {
    handleKeyDown,
    handleKeyUp
  };
};

/**
 * Custom drag layer for showing drag preview
 */
export const useDragLayer = () => {
  // This can be implemented later for custom drag previews
  return {
    isDragging: false,
    item: null,
    currentOffset: null
  };
};
