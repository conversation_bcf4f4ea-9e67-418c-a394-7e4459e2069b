import { 
  FileSystemNode, 
  FileOperation, 
  FileWatchEvent,
  FileSearchOptions,
  FileSearchResult 
} from '../../shared/types/FileExplorer';

declare global {
  interface Window {
    electronAPI: {
      // File system operations
      'fs:read-directory': (dirPath: string) => Promise<FileSystemNode[]>;
      'fs:get-file-info': (filePath: string) => Promise<FileSystemNode | null>;
      'fs:perform-operation': (operation: FileOperation) => Promise<boolean>;
      'fs:show-open-dialog': (options?: any) => Promise<string[]>;
      'fs:show-save-dialog': (options?: any) => Promise<string | null>;
      'fs:start-watching': (dirPath: string) => Promise<void>;
      'fs:stop-watching': (dirPath: string) => Promise<void>;
      
      // Event listeners
      on: (channel: string, callback: (...args: any[]) => void) => void;
      off: (channel: string, callback: (...args: any[]) => void) => void;
    };
  }
}

export class FileSystemService {
  private watchCallbacks = new Map<string, (event: FileWatchEvent) => void>();

  /**
   * Read directory contents
   */
  async readDirectory(dirPath: string): Promise<FileSystemNode[]> {
    try {
      return await window.electronAPI['fs:read-directory'](dirPath);
    } catch (error) {
      console.error('Error reading directory:', error);
      throw error;
    }
  }

  /**
   * Get file information
   */
  async getFileInfo(filePath: string): Promise<FileSystemNode | null> {
    try {
      return await window.electronAPI['fs:get-file-info'](filePath);
    } catch (error) {
      console.error('Error getting file info:', error);
      throw error;
    }
  }

  /**
   * Perform file operation
   */
  async performOperation(operation: FileOperation): Promise<boolean> {
    try {
      return await window.electronAPI['fs:perform-operation'](operation);
    } catch (error) {
      console.error('Error performing operation:', error);
      throw error;
    }
  }

  /**
   * Show open dialog
   */
  async showOpenDialog(options?: {
    title?: string;
    defaultPath?: string;
    buttonLabel?: string;
    filters?: Array<{ name: string; extensions: string[] }>;
    properties?: Array<'openFile' | 'openDirectory' | 'multiSelections' | 'showHiddenFiles'>;
  }): Promise<string[]> {
    try {
      return await window.electronAPI['fs:show-open-dialog'](options);
    } catch (error) {
      console.error('Error showing open dialog:', error);
      throw error;
    }
  }

  /**
   * Show save dialog
   */
  async showSaveDialog(options?: {
    title?: string;
    defaultPath?: string;
    buttonLabel?: string;
    filters?: Array<{ name: string; extensions: string[] }>;
  }): Promise<string | null> {
    try {
      return await window.electronAPI['fs:show-save-dialog'](options);
    } catch (error) {
      console.error('Error showing save dialog:', error);
      throw error;
    }
  }

  /**
   * Start watching directory for changes
   */
  async startWatching(dirPath: string, callback: (event: FileWatchEvent) => void): Promise<void> {
    try {
      // Store callback for this path
      this.watchCallbacks.set(dirPath, callback);

      // Set up event listener for file changes
      const handleFileChange = (event: FileWatchEvent) => {
        const cb = this.watchCallbacks.get(dirPath);
        if (cb) {
          cb(event);
        }
      };

      window.electronAPI.on('fs:file-changed', handleFileChange);
      
      // Start watching
      await window.electronAPI['fs:start-watching'](dirPath);
    } catch (error) {
      console.error('Error starting file watch:', error);
      throw error;
    }
  }

  /**
   * Stop watching directory
   */
  async stopWatching(dirPath: string): Promise<void> {
    try {
      await window.electronAPI['fs:stop-watching'](dirPath);
      this.watchCallbacks.delete(dirPath);
    } catch (error) {
      console.error('Error stopping file watch:', error);
      throw error;
    }
  }

  /**
   * Search files (client-side implementation)
   */
  async searchFiles(
    rootPath: string, 
    options: FileSearchOptions
  ): Promise<FileSearchResult[]> {
    const results: FileSearchResult[] = [];
    
    try {
      const searchInDirectory = async (dirPath: string, depth: number = 0): Promise<void> => {
        if (depth > 10) return; // Prevent infinite recursion

        const nodes = await this.readDirectory(dirPath);
        
        for (const node of nodes) {
          // Skip hidden files if not searching them
          if (node.isHidden && !options.query.startsWith('.')) {
            continue;
          }

          // Check filename match
          const nameMatch = this.matchesQuery(node.name, options.query, options.caseSensitive);
          const pathMatch = this.matchesQuery(node.path, options.query, options.caseSensitive);

          if (nameMatch || pathMatch) {
            results.push({
              node,
              matches: [
                ...(nameMatch ? [{
                  type: 'filename' as const,
                  text: node.name,
                  startIndex: nameMatch.index,
                  endIndex: nameMatch.index + nameMatch.length
                }] : []),
                ...(pathMatch ? [{
                  type: 'path' as const,
                  text: node.path,
                  startIndex: pathMatch.index,
                  endIndex: pathMatch.index + pathMatch.length
                }] : [])
              ],
              score: nameMatch ? 1.0 : 0.5 // Filename matches score higher
            });
          }

          // Recursively search subdirectories
          if (node.type === 'directory') {
            await searchInDirectory(node.path, depth + 1);
          }

          // Stop if we've reached max results
          if (options.maxResults && results.length >= options.maxResults) {
            return;
          }
        }
      };

      await searchInDirectory(rootPath);

      // Sort results by score (descending)
      results.sort((a, b) => b.score - a.score);

      return results.slice(0, options.maxResults || 100);
    } catch (error) {
      console.error('Error searching files:', error);
      throw error;
    }
  }

  /**
   * Check if text matches query
   */
  private matchesQuery(
    text: string, 
    query: string, 
    caseSensitive: boolean = false
  ): { index: number; length: number } | null {
    const searchText = caseSensitive ? text : text.toLowerCase();
    const searchQuery = caseSensitive ? query : query.toLowerCase();
    
    const index = searchText.indexOf(searchQuery);
    if (index !== -1) {
      return { index, length: query.length };
    }
    
    return null;
  }

  /**
   * Get parent directory path
   */
  getParentPath(filePath: string): string {
    const parts = filePath.split(/[/\\]/);
    parts.pop();
    return parts.join('/') || '/';
  }

  /**
   * Join paths
   */
  joinPath(...parts: string[]): string {
    return parts.join('/').replace(/\/+/g, '/');
  }

  /**
   * Get filename from path
   */
  getFileName(filePath: string): string {
    return filePath.split(/[/\\]/).pop() || '';
  }

  /**
   * Get directory name from path
   */
  getDirName(filePath: string): string {
    const parts = filePath.split(/[/\\]/);
    parts.pop();
    return parts.join('/') || '/';
  }

  /**
   * Check if path is absolute
   */
  isAbsolutePath(filePath: string): boolean {
    return filePath.startsWith('/') || /^[A-Za-z]:/.test(filePath);
  }

  /**
   * Normalize path separators
   */
  normalizePath(filePath: string): string {
    return filePath.replace(/\\/g, '/');
  }

  /**
   * Get file extension
   */
  getFileExtension(fileName: string): string {
    const lastDotIndex = fileName.lastIndexOf('.');
    if (lastDotIndex === -1 || lastDotIndex === 0) {
      return '';
    }
    return fileName.substring(lastDotIndex);
  }

  /**
   * Check if file exists (by trying to get info)
   */
  async fileExists(filePath: string): Promise<boolean> {
    try {
      const info = await this.getFileInfo(filePath);
      return info !== null;
    } catch {
      return false;
    }
  }

  /**
   * Generate unique filename if file already exists
   */
  async generateUniqueFileName(basePath: string, fileName: string): Promise<string> {
    let counter = 1;
    let newFileName = fileName;
    const extension = this.getFileExtension(fileName);
    const nameWithoutExt = fileName.substring(0, fileName.length - extension.length);

    while (await this.fileExists(this.joinPath(basePath, newFileName))) {
      newFileName = `${nameWithoutExt} (${counter})${extension}`;
      counter++;
    }

    return newFileName;
  }
}

// Create singleton instance
export const fileSystemService = new FileSystemService();
