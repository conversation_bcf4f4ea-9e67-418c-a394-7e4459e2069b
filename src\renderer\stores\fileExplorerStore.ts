import { create } from 'zustand';
import { subscribeWithSelector } from 'zustand/middleware';
import {
  FileSystemNode,
  FileExplorerState,
  FileSortBy,
  SortOrder,
  FileViewMode,
  FileOperation,
  FileSearchOptions,
  FileWatchEvent,
  FileNodeType,
} from '../../shared/types/FileExplorer';
import { fileSystemService } from '../services/fileSystemService';

interface FileExplorerStore extends FileExplorerState {
  // Actions
  setRootPath: (path: string) => void;
  setCurrentPath: (path: string) => void;
  loadDirectory: (path: string) => Promise<void>;
  expandNode: (nodeId: string) => Promise<void>;
  collapseNode: (nodeId: string) => void;
  selectNode: (nodeId: string, multiSelect?: boolean) => void;
  deselectNode: (nodeId: string) => void;
  clearSelection: () => void;
  refreshNode: (nodeId: string) => Promise<void>;
  refreshAll: () => Promise<void>;
  search: (options: FileSearchOptions) => Promise<void>;
  clearSearch: () => void;
  performOperation: (operation: FileOperation) => Promise<boolean>;
  navigateTo: (path: string) => Promise<void>;
  goUp: () => Promise<void>;
  setSortBy: (sortBy: FileSortBy, order?: SortOrder) => void;
  setViewMode: (mode: FileViewMode) => void;
  toggleHiddenFiles: () => void;
  setError: (error: string | undefined) => void;
  setLoading: (loading: boolean) => void;

  // File watching
  startWatching: (path: string) => Promise<void>;
  stopWatching: (path: string) => Promise<void>;
  handleFileChange: (event: FileWatchEvent) => void;
}

export const useFileExplorerStore = create<FileExplorerStore>()(
  subscribeWithSelector((set, get) => ({
    // Initial state
    rootPath: '',
    currentPath: '',
    nodes: new Map(),
    expandedNodes: new Set(),
    selectedNodes: new Set(),
    searchQuery: '',
    filteredNodes: [],
    isLoading: false,
    error: undefined,
    sortBy: FileSortBy.NAME,
    sortOrder: SortOrder.ASC,
    showHiddenFiles: false,
    viewMode: FileViewMode.TREE,

    // Actions
    setRootPath: (path: string) => {
      set({ rootPath: path, currentPath: path });
    },

    setCurrentPath: (path: string) => {
      set({ currentPath: path });
    },

    loadDirectory: async (path: string) => {
      const { setLoading, setError } = get();

      try {
        setLoading(true);
        setError(undefined);

        const children = await fileSystemService.readDirectory(path);

        set(state => {
          const newNodes = new Map(state.nodes);

          // Update or create parent node
          const parentNode = newNodes.get(path) || {
            id: path,
            name: fileSystemService.getFileName(path) || 'Root',
            path: path,
            type: FileNodeType.DIRECTORY,
            depth: 0,
            isExpanded: false,
            isSelected: false,
            isLoading: false,
            children: [],
            icon: 'folder',
          };

          parentNode.children = children;
          parentNode.isLoading = false;
          newNodes.set(path, parentNode);

          // Add all children to nodes map
          children.forEach(child => {
            newNodes.set(child.id, child);
          });

          return { nodes: newNodes };
        });
      } catch (error) {
        setError(error instanceof Error ? error.message : 'Failed to load directory');
        console.error('Error loading directory:', error);
      } finally {
        setLoading(false);
      }
    },

    expandNode: async (nodeId: string) => {
      const { nodes, expandedNodes, loadDirectory } = get();
      const node = nodes.get(nodeId);

      if (!node || node.type !== 'directory') return;

      if (!expandedNodes.has(nodeId)) {
        set(state => ({
          expandedNodes: new Set([...state.expandedNodes, nodeId]),
        }));

        // Load children if not already loaded
        if (!node.children || node.children.length === 0) {
          await loadDirectory(nodeId);
        }
      }
    },

    collapseNode: (nodeId: string) => {
      set(state => {
        const newExpandedNodes = new Set(state.expandedNodes);
        newExpandedNodes.delete(nodeId);
        return { expandedNodes: newExpandedNodes };
      });
    },

    selectNode: (nodeId: string, multiSelect = false) => {
      set(state => {
        if (multiSelect) {
          const newSelectedNodes = new Set(state.selectedNodes);
          if (newSelectedNodes.has(nodeId)) {
            newSelectedNodes.delete(nodeId);
          } else {
            newSelectedNodes.add(nodeId);
          }
          return { selectedNodes: newSelectedNodes };
        } else {
          return { selectedNodes: new Set([nodeId]) };
        }
      });
    },

    deselectNode: (nodeId: string) => {
      set(state => {
        const newSelectedNodes = new Set(state.selectedNodes);
        newSelectedNodes.delete(nodeId);
        return { selectedNodes: newSelectedNodes };
      });
    },

    clearSelection: () => {
      set({ selectedNodes: new Set() });
    },

    refreshNode: async (nodeId: string) => {
      const { loadDirectory } = get();
      await loadDirectory(nodeId);
    },

    refreshAll: async () => {
      const { rootPath, loadDirectory } = get();
      if (rootPath) {
        await loadDirectory(rootPath);
      }
    },

    search: async (options: FileSearchOptions) => {
      const { rootPath, setLoading, setError } = get();

      try {
        setLoading(true);
        setError(undefined);

        const results = await fileSystemService.searchFiles(rootPath, options);

        set({
          searchQuery: options.query,
          filteredNodes: results.map(result => result.node.id),
        });
      } catch (error) {
        setError(error instanceof Error ? error.message : 'Search failed');
        console.error('Error searching files:', error);
      } finally {
        setLoading(false);
      }
    },

    clearSearch: () => {
      set({
        searchQuery: '',
        filteredNodes: [],
      });
    },

    performOperation: async (operation: FileOperation) => {
      const { setLoading, setError, refreshNode } = get();

      try {
        setLoading(true);
        setError(undefined);

        const success = await fileSystemService.performOperation(operation);

        if (success) {
          // Refresh the parent directory
          const parentPath = fileSystemService.getDirName(operation.source);
          await refreshNode(parentPath);

          if (operation.target) {
            const targetParentPath = fileSystemService.getDirName(operation.target);
            if (targetParentPath !== parentPath) {
              await refreshNode(targetParentPath);
            }
          }
        }

        return success;
      } catch (error) {
        setError(error instanceof Error ? error.message : 'Operation failed');
        console.error('Error performing operation:', error);
        return false;
      } finally {
        setLoading(false);
      }
    },

    navigateTo: async (path: string) => {
      const { setCurrentPath, loadDirectory } = get();
      setCurrentPath(path);
      await loadDirectory(path);
    },

    goUp: async () => {
      const { currentPath, navigateTo } = get();
      const parentPath = fileSystemService.getParentPath(currentPath);
      if (parentPath !== currentPath) {
        await navigateTo(parentPath);
      }
    },

    setSortBy: (sortBy: FileSortBy, order?: SortOrder) => {
      set(state => ({
        sortBy,
        sortOrder:
          order ||
          (state.sortBy === sortBy && state.sortOrder === SortOrder.ASC
            ? SortOrder.DESC
            : SortOrder.ASC),
      }));
    },

    setViewMode: (mode: FileViewMode) => {
      set({ viewMode: mode });
    },

    toggleHiddenFiles: () => {
      set(state => ({ showHiddenFiles: !state.showHiddenFiles }));
    },

    setError: (error: string | undefined) => {
      set({ error });
    },

    setLoading: (loading: boolean) => {
      set({ isLoading: loading });
    },

    startWatching: async (path: string) => {
      const { handleFileChange } = get();
      await fileSystemService.startWatching(path, handleFileChange);
    },

    stopWatching: async (path: string) => {
      await fileSystemService.stopWatching(path);
    },

    handleFileChange: (event: FileWatchEvent) => {
      const { refreshNode } = get();

      // Refresh the parent directory of the changed file
      const parentPath = fileSystemService.getDirName(event.path);
      void refreshNode(parentPath);
    },
  }))
);

// Helper hook to get selected nodes
export const useSelectedNodes = () => {
  const { nodes, selectedNodes } = useFileExplorerStore();
  return Array.from(selectedNodes)
    .map(id => nodes.get(id))
    .filter(Boolean) as FileSystemNode[];
};

// Helper hook to check if a node is expanded
export const useIsNodeExpanded = (nodeId: string) => {
  return useFileExplorerStore(state => state.expandedNodes.has(nodeId));
};

// Helper hook to check if a node is selected
export const useIsNodeSelected = (nodeId: string) => {
  return useFileExplorerStore(state => state.selectedNodes.has(nodeId));
};
