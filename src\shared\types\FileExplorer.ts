// File Explorer type definitions

export interface FileSystemNode {
  id: string;
  name: string;
  path: string;
  type: FileNodeType;
  size?: number;
  lastModified?: Date;
  isExpanded?: boolean;
  isSelected?: boolean;
  isLoading?: boolean;
  children?: FileSystemNode[];
  parent?: string;
  depth: number;
  icon?: string;
  extension?: string;
  isHidden?: boolean;
  permissions?: FilePermissions;
  metadata?: FileMetadata;
}

export enum FileNodeType {
  FILE = 'file',
  DIRECTORY = 'directory',
  SYMLINK = 'symlink',
  UNKNOWN = 'unknown'
}

export interface FilePermissions {
  readable: boolean;
  writable: boolean;
  executable: boolean;
}

export interface FileMetadata {
  mimeType?: string;
  encoding?: string;
  lineCount?: number;
  wordCount?: number;
  characterCount?: number;
  isText?: boolean;
  isBinary?: boolean;
  isImage?: boolean;
  isDocument?: boolean;
  isArchive?: boolean;
  isExecutable?: boolean;
}

export interface FileExplorerState {
  rootPath: string;
  currentPath: string;
  nodes: Map<string, FileSystemNode>;
  expandedNodes: Set<string>;
  selectedNodes: Set<string>;
  searchQuery: string;
  filteredNodes: string[];
  isLoading: boolean;
  error?: string;
  sortBy: FileSortBy;
  sortOrder: SortOrder;
  showHiddenFiles: boolean;
  viewMode: FileViewMode;
}

export enum FileSortBy {
  NAME = 'name',
  SIZE = 'size',
  TYPE = 'type',
  MODIFIED = 'modified',
  EXTENSION = 'extension'
}

export enum SortOrder {
  ASC = 'asc',
  DESC = 'desc'
}

export enum FileViewMode {
  TREE = 'tree',
  LIST = 'list',
  GRID = 'grid'
}

export interface FileOperation {
  type: FileOperationType;
  source: string;
  target?: string;
  options?: FileOperationOptions;
}

export enum FileOperationType {
  CREATE_FILE = 'create_file',
  CREATE_FOLDER = 'create_folder',
  DELETE = 'delete',
  RENAME = 'rename',
  MOVE = 'move',
  COPY = 'copy',
  CUT = 'cut',
  PASTE = 'paste'
}

export interface FileOperationOptions {
  overwrite?: boolean;
  recursive?: boolean;
  preserveTimestamps?: boolean;
  followSymlinks?: boolean;
}

export interface FileContextMenuAction {
  id: string;
  label: string;
  icon?: string;
  shortcut?: string;
  action: (node: FileSystemNode) => void;
  enabled?: (node: FileSystemNode) => boolean;
  visible?: (node: FileSystemNode) => boolean;
  separator?: boolean;
  submenu?: FileContextMenuAction[];
}

export interface FileDragDropData {
  nodeIds: string[];
  operation: 'move' | 'copy';
  sourceExplorer?: string;
}

export interface FileSearchOptions {
  query: string;
  caseSensitive?: boolean;
  useRegex?: boolean;
  includeContent?: boolean;
  fileTypes?: string[];
  excludePatterns?: string[];
  maxResults?: number;
}

export interface FileSearchResult {
  node: FileSystemNode;
  matches: FileSearchMatch[];
  score: number;
}

export interface FileSearchMatch {
  type: 'filename' | 'content' | 'path';
  text: string;
  startIndex: number;
  endIndex: number;
  lineNumber?: number;
  columnNumber?: number;
}

export interface BreadcrumbItem {
  id: string;
  label: string;
  path: string;
  icon?: string;
  isClickable: boolean;
}

export interface FileTypeDefinition {
  extensions: string[];
  icon: string;
  color?: string;
  category: FileCategory;
  mimeTypes?: string[];
  description?: string;
  isText?: boolean;
  isBinary?: boolean;
  canPreview?: boolean;
  canEdit?: boolean;
}

export enum FileCategory {
  DOCUMENT = 'document',
  IMAGE = 'image',
  VIDEO = 'video',
  AUDIO = 'audio',
  ARCHIVE = 'archive',
  CODE = 'code',
  DATA = 'data',
  EXECUTABLE = 'executable',
  FONT = 'font',
  MARKUP = 'markup',
  STYLESHEET = 'stylesheet',
  CONFIG = 'config',
  LOG = 'log',
  UNKNOWN = 'unknown'
}

export interface FileExplorerConfig {
  showLineNumbers?: boolean;
  showFileSize?: boolean;
  showLastModified?: boolean;
  showFileIcons?: boolean;
  enableVirtualization?: boolean;
  itemHeight?: number;
  indentSize?: number;
  maxDepth?: number;
  autoExpandDepth?: number;
  enableDragDrop?: boolean;
  enableContextMenu?: boolean;
  enableSearch?: boolean;
  enableBreadcrumbs?: boolean;
  refreshInterval?: number;
  watchFileChanges?: boolean;
}

export interface FileWatchEvent {
  type: 'created' | 'modified' | 'deleted' | 'moved';
  path: string;
  oldPath?: string;
  timestamp: Date;
  node?: FileSystemNode;
}

export interface FileExplorerActions {
  loadDirectory: (path: string) => Promise<void>;
  expandNode: (nodeId: string) => Promise<void>;
  collapseNode: (nodeId: string) => void;
  selectNode: (nodeId: string, multiSelect?: boolean) => void;
  deselectNode: (nodeId: string) => void;
  clearSelection: () => void;
  refreshNode: (nodeId: string) => Promise<void>;
  refreshAll: () => Promise<void>;
  search: (options: FileSearchOptions) => Promise<FileSearchResult[]>;
  clearSearch: () => void;
  performOperation: (operation: FileOperation) => Promise<void>;
  navigateTo: (path: string) => Promise<void>;
  goUp: () => Promise<void>;
  goBack: () => void;
  goForward: () => void;
  setSortBy: (sortBy: FileSortBy, order?: SortOrder) => void;
  setViewMode: (mode: FileViewMode) => void;
  toggleHiddenFiles: () => void;
}

export interface FileExplorerHooks {
  onNodeSelect?: (node: FileSystemNode) => void;
  onNodeDoubleClick?: (node: FileSystemNode) => void;
  onNodeRightClick?: (node: FileSystemNode, event: React.MouseEvent) => void;
  onNodeExpand?: (node: FileSystemNode) => void;
  onNodeCollapse?: (node: FileSystemNode) => void;
  onDirectoryChange?: (path: string) => void;
  onFileOperation?: (operation: FileOperation, result: boolean) => void;
  onError?: (error: Error) => void;
  onDragStart?: (nodes: FileSystemNode[], event: React.DragEvent) => void;
  onDragEnd?: (nodes: FileSystemNode[], event: React.DragEvent) => void;
  onDrop?: (dragData: FileDragDropData, targetNode: FileSystemNode) => void;
}
