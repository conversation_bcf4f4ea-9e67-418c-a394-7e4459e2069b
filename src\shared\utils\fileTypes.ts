import { FileTypeDefinition, FileCategory } from '../types/FileExplorer';

// File type definitions with icons and categories
export const FILE_TYPE_DEFINITIONS: Record<string, FileTypeDefinition> = {
  // Documents
  pdf: {
    extensions: ['.pdf'],
    icon: 'picture_as_pdf',
    color: '#dc2626',
    category: FileCategory.DOCUMENT,
    mimeTypes: ['application/pdf'],
    description: 'PDF Document',
    isBinary: true,
    canPreview: true,
    canEdit: false
  },
  word: {
    extensions: ['.doc', '.docx'],
    icon: 'description',
    color: '#2563eb',
    category: FileCategory.DOCUMENT,
    mimeTypes: ['application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'],
    description: 'Microsoft Word Document',
    isBinary: true,
    canPreview: true,
    canEdit: false
  },
  excel: {
    extensions: ['.xls', '.xlsx'],
    icon: 'table_chart',
    color: '#16a34a',
    category: FileCategory.DOCUMENT,
    mimeTypes: ['application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'],
    description: 'Microsoft Excel Spreadsheet',
    isBinary: true,
    canPreview: true,
    canEdit: false
  },
  powerpoint: {
    extensions: ['.ppt', '.pptx'],
    icon: 'slideshow',
    color: '#ea580c',
    category: FileCategory.DOCUMENT,
    mimeTypes: ['application/vnd.ms-powerpoint', 'application/vnd.openxmlformats-officedocument.presentationml.presentation'],
    description: 'Microsoft PowerPoint Presentation',
    isBinary: true,
    canPreview: true,
    canEdit: false
  },
  
  // Text and Code
  text: {
    extensions: ['.txt', '.text'],
    icon: 'article',
    color: '#6b7280',
    category: FileCategory.DOCUMENT,
    mimeTypes: ['text/plain'],
    description: 'Text File',
    isText: true,
    canPreview: true,
    canEdit: true
  },
  markdown: {
    extensions: ['.md', '.markdown'],
    icon: 'article',
    color: '#374151',
    category: FileCategory.MARKUP,
    mimeTypes: ['text/markdown'],
    description: 'Markdown File',
    isText: true,
    canPreview: true,
    canEdit: true
  },
  javascript: {
    extensions: ['.js', '.jsx', '.mjs'],
    icon: 'code',
    color: '#f59e0b',
    category: FileCategory.CODE,
    mimeTypes: ['application/javascript', 'text/javascript'],
    description: 'JavaScript File',
    isText: true,
    canPreview: true,
    canEdit: true
  },
  typescript: {
    extensions: ['.ts', '.tsx'],
    icon: 'code',
    color: '#3b82f6',
    category: FileCategory.CODE,
    mimeTypes: ['application/typescript'],
    description: 'TypeScript File',
    isText: true,
    canPreview: true,
    canEdit: true
  },
  python: {
    extensions: ['.py', '.pyw'],
    icon: 'code',
    color: '#3776ab',
    category: FileCategory.CODE,
    mimeTypes: ['text/x-python'],
    description: 'Python File',
    isText: true,
    canPreview: true,
    canEdit: true
  },
  
  // Images
  image: {
    extensions: ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.svg'],
    icon: 'image',
    color: '#8b5cf6',
    category: FileCategory.IMAGE,
    mimeTypes: ['image/jpeg', 'image/png', 'image/gif', 'image/bmp', 'image/webp', 'image/svg+xml'],
    description: 'Image File',
    isBinary: true,
    canPreview: true,
    canEdit: false
  },
  
  // Archives
  archive: {
    extensions: ['.zip', '.rar', '.7z', '.tar', '.gz', '.bz2'],
    icon: 'archive',
    color: '#7c3aed',
    category: FileCategory.ARCHIVE,
    mimeTypes: ['application/zip', 'application/x-rar-compressed', 'application/x-7z-compressed'],
    description: 'Archive File',
    isBinary: true,
    canPreview: false,
    canEdit: false
  },
  
  // Data
  csv: {
    extensions: ['.csv'],
    icon: 'table_view',
    color: '#059669',
    category: FileCategory.DATA,
    mimeTypes: ['text/csv'],
    description: 'CSV File',
    isText: true,
    canPreview: true,
    canEdit: true
  },
  json: {
    extensions: ['.json'],
    icon: 'data_object',
    color: '#f59e0b',
    category: FileCategory.DATA,
    mimeTypes: ['application/json'],
    description: 'JSON File',
    isText: true,
    canPreview: true,
    canEdit: true
  },
  xml: {
    extensions: ['.xml'],
    icon: 'code',
    color: '#dc2626',
    category: FileCategory.MARKUP,
    mimeTypes: ['application/xml', 'text/xml'],
    description: 'XML File',
    isText: true,
    canPreview: true,
    canEdit: true
  },
  
  // Config
  config: {
    extensions: ['.ini', '.conf', '.config', '.env'],
    icon: 'settings',
    color: '#6b7280',
    category: FileCategory.CONFIG,
    mimeTypes: ['text/plain'],
    description: 'Configuration File',
    isText: true,
    canPreview: true,
    canEdit: true
  },
  
  // Default folder
  folder: {
    extensions: [],
    icon: 'folder',
    color: '#3b82f6',
    category: FileCategory.UNKNOWN,
    description: 'Folder',
    canPreview: false,
    canEdit: false
  },
  
  // Default file
  unknown: {
    extensions: [],
    icon: 'insert_drive_file',
    color: '#6b7280',
    category: FileCategory.UNKNOWN,
    description: 'Unknown File Type',
    canPreview: false,
    canEdit: false
  }
};

/**
 * Get file type definition based on file extension
 */
export function getFileType(fileName: string): FileTypeDefinition {
  const extension = getFileExtension(fileName).toLowerCase();
  
  // Find matching file type
  for (const [key, definition] of Object.entries(FILE_TYPE_DEFINITIONS)) {
    if (definition.extensions.includes(extension)) {
      return definition;
    }
  }
  
  return FILE_TYPE_DEFINITIONS.unknown;
}

/**
 * Get file extension from filename
 */
export function getFileExtension(fileName: string): string {
  const lastDotIndex = fileName.lastIndexOf('.');
  if (lastDotIndex === -1 || lastDotIndex === 0) {
    return '';
  }
  return fileName.substring(lastDotIndex);
}

/**
 * Get file icon based on file type
 */
export function getFileIcon(fileName: string, isDirectory: boolean = false): string {
  if (isDirectory) {
    return FILE_TYPE_DEFINITIONS.folder.icon;
  }
  
  const fileType = getFileType(fileName);
  return fileType.icon;
}

/**
 * Get file color based on file type
 */
export function getFileColor(fileName: string, isDirectory: boolean = false): string {
  if (isDirectory) {
    return FILE_TYPE_DEFINITIONS.folder.color || '#3b82f6';
  }
  
  const fileType = getFileType(fileName);
  return fileType.color || '#6b7280';
}

/**
 * Check if file is text-based
 */
export function isTextFile(fileName: string): boolean {
  const fileType = getFileType(fileName);
  return fileType.isText || false;
}

/**
 * Check if file is binary
 */
export function isBinaryFile(fileName: string): boolean {
  const fileType = getFileType(fileName);
  return fileType.isBinary || false;
}

/**
 * Check if file can be previewed
 */
export function canPreviewFile(fileName: string): boolean {
  const fileType = getFileType(fileName);
  return fileType.canPreview || false;
}

/**
 * Check if file can be edited
 */
export function canEditFile(fileName: string): boolean {
  const fileType = getFileType(fileName);
  return fileType.canEdit || false;
}

/**
 * Get file category
 */
export function getFileCategory(fileName: string): FileCategory {
  const fileType = getFileType(fileName);
  return fileType.category;
}

/**
 * Format file size in human readable format
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 B';
  
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
}

/**
 * Format date in human readable format
 */
export function formatDate(date: Date): string {
  const now = new Date();
  const diffMs = now.getTime() - date.getTime();
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
  
  if (diffDays === 0) {
    return 'Today';
  } else if (diffDays === 1) {
    return 'Yesterday';
  } else if (diffDays < 7) {
    return `${diffDays} days ago`;
  } else {
    return date.toLocaleDateString();
  }
}
